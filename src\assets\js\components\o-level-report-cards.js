// O-Level Report Cards Component
// Generate and manage O-Level student report cards

const OLevelReportCardsComponent = {
  // Component state
  state: {
    students: [],
    classes: [],
    academicYears: [],
    terms: [],
    selectedClass: '',
    selectedTerm: '',
    selectedAcademicYear: '',
    selectedStudents: [],
    schoolSettings: {},
    loading: false
  },

  // Current preview data for PDF download
  currentPreviewData: null,

  // Initialize component (called by ComponentLifecycleManager after rendering)
  async init() {
    console.log('🔧 Initializing O-Level Report Cards Component...');

    // Reset component state
    this.resetComponentState();

    // Check academic context first
    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) {
      return;
    }

    try {
      this.state.loading = true;
      await this.loadInitialData();

      // Populate dropdowns after data is loaded with a small delay to ensure DOM is ready
      setTimeout(() => {
        this.populateDropdowns();
      }, 100);

      this.initializeEventListeners();
      return true;
    } catch (error) {
      console.error('❌ O-Level Report Cards initialization failed:', error);
      this.showError('Failed to initialize report cards system');
      return false;
    } finally {
      this.state.loading = false;
    }
  },

  // Reset component state
  resetComponentState() {
    this.state.students = [];
    this.state.selectedClass = '';
    this.state.selectedStudents = [];
    this.currentPreviewData = null;

    // Reset academic context references
    this.state.currentAcademicYear = null;
    this.state.currentTerm = null;
    this.state.selectedAcademicYear = '';
    this.state.selectedTerm = '';

  },

  // Cleanup component
  cleanup() {
    // Clean up any event listeners or resources
    this.resetComponentState();
  },

  // Load initial data
  async loadInitialData() {
    try {

      // Get current academic context
      this.state.currentAcademicYear = window.AcademicContext.getActiveAcademicYear();
      this.state.currentTerm = window.AcademicContext.getActiveTerm();

      if (!this.state.currentAcademicYear || !this.state.currentTerm) {
        throw new Error('No active academic year or term found');
      }

      // Load data for current academic context and historical data
      const [classesResult, academicYearsResult, termsResult, schoolResult] = await Promise.all([
        window.ClassesAPI.getAll({ level: 'o_level' }), // Filter for O-Level classes at API level
        window.AcademicYearsAPI.getAll(), // Load all academic years for historical access
        window.TermsAPI.getAll(), // Load all terms for historical access
        window.SchoolSettingsAPI.getAll()
      ]);

      if (classesResult.success) {
        // Classes are already filtered for O-Level at API level
        this.state.classes = classesResult.data || [];
      }

      // Load all academic years and terms for historical data access
      if (academicYearsResult.success) {
        this.state.academicYears = academicYearsResult.data || [];
      } else {
        this.state.academicYears = [];
        console.warn('Failed to load academic years:', academicYearsResult);
      }

      if (termsResult.success) {
        this.state.terms = termsResult.data || [];
      } else {
        this.state.terms = [];
        console.warn('Failed to load terms:', termsResult);
      }

      // Academic year and term will be set during dropdown population

      if (schoolResult.success) {
        // Convert array of settings to object for easier access
        this.state.schoolSettings = {};
        schoolResult.data.forEach(setting => {
          this.state.schoolSettings[setting.setting_key] = setting.setting_value;
        });
      }


      if (this.state.terms.length > 0) {
        console.log('📋 Sample terms data:', this.state.terms.slice(0, 2));
      }
    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      throw error;
    }
  },

  // Render component (returns HTML string for page router)
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'O-Level Report Cards',
          `Generate comprehensive student report cards`
        )}

        <!-- Selection Filters -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-filter', 'base', 'primary-600')}
              <span class="ml-3">Select Parameters</span>
            </h3>
          </div>

          <div class="p-6">
            <form id="report-filters-form" class="space-y-6">
              <!-- Academic Context (Defaulted to current, but allows historical selection) -->
              <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
                ${SRDesignSystem.forms.select('academic_year', 'Academic Year', [], '')}
                ${SRDesignSystem.forms.select('term', 'Term', [], '')}
                ${SRDesignSystem.forms.select('class', 'Class', [], '')}
              </div>

              <!-- Generate Button -->
              <div class="flex justify-end">
                ${SRDesignSystem.forms.button('load-students', 'Load Students', 'primary', {
                  onclick: 'OLevelReportCardsComponent.loadStudents()',
                  icon: 'fas fa-search'
                })}
              </div>
            </form>
          </div>
        </div>

        <!-- Students Selection -->
        <div id="students-section" class="hidden bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <!-- Enhanced Header with Student Count -->
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-5 border-b border-primary-200">
            <div class="flex items-center justify-between">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
                ${SRDesignSystem.components.icon('fas fa-users', 'lg', 'primary-600')}
                <span class="ml-3">Students Selection</span>
              </h3>
              <div class="flex items-center space-x-4">
                <span class="${SRDesignSystem.responsive.text.sm} text-primary-700 font-medium" id="students-count">
                  <!-- Student count will be displayed here -->
                </span>
              </div>
            </div>
          </div>

          <div class="p-6 space-y-6">
            <!-- Enhanced Search and Controls Bar -->
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div class="flex items-center gap-4">
                <!-- Search Input (2/3 width) -->
                <div class="flex-1" style="flex: 2;">
                  <div class="relative">
                    ${SRDesignSystem.forms.input('student_search', '', '', {
                      placeholder: 'Search by name or admission number...',
                      icon: 'fas fa-search'
                    })}
                  </div>
                </div>

                <!-- Select All Control (1/3 width) -->
                <div class="flex items-center justify-end" style="flex: 1;">
                  <label class="flex items-center cursor-pointer group bg-white rounded-lg px-4 py-2 border border-gray-300 hover:border-primary-300 hover:bg-primary-50 transition-all duration-200">
                    <input type="checkbox"
                           id="select-all-students"
                           class="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500 focus:ring-2 transition-colors"
                           onchange="OLevelReportCardsComponent.toggleAllStudents()">
                    <span class="ml-3 ${SRDesignSystem.responsive.text.sm} font-medium text-gray-700 group-hover:text-primary-700 transition-colors">
                      Select All Students
                    </span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Enhanced Students List Container -->
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <h4 class="${SRDesignSystem.responsive.text.base} font-medium text-gray-900">Available Students</h4>
                <div class="${SRDesignSystem.responsive.text.sm} text-gray-500" id="selection-status">
                  <!-- Selection status will be displayed here -->
                </div>
              </div>

              <div id="students-list" class="space-y-3 max-h-80 overflow-y-auto border border-gray-200 rounded-lg bg-gray-50 p-4">
                <div class="text-center py-8 text-gray-500">
                  <div class="mb-3">
                    ${SRDesignSystem.components.icon('fas fa-users', '3xl', 'gray-300')}
                  </div>
                  <p class="${SRDesignSystem.responsive.text.base} font-medium text-gray-600 mb-1">No Students Loaded</p>
                  <p class="${SRDesignSystem.responsive.text.sm} text-gray-500">Students will appear here after selecting parameters above</p>
                </div>
              </div>
            </div>

            <!-- Enhanced Action Buttons -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-200">
              <div class="${SRDesignSystem.responsive.text.sm} text-gray-600">
                <span class="flex items-center">
                  ${SRDesignSystem.components.icon('fas fa-info-circle', 'sm', 'gray-400')}
                  <span class="ml-2">Select students to generate their report cards</span>
                </span>
              </div>

              <div class="flex items-center space-x-3">
                ${SRDesignSystem.forms.button('preview-reports', 'Preview', 'secondary', {
                  onclick: 'OLevelReportCardsComponent.previewReports()',
                  icon: 'fas fa-eye'
                })}

                ${SRDesignSystem.forms.button('generate-reports', 'Generate', 'primary', {
                  onclick: 'OLevelReportCardsComponent.generateReports()',
                  icon: 'fas fa-file-pdf'
                })}
              </div>
            </div>
          </div>
        </div>

        <!-- Report Preview -->
        <div id="report-preview-section" class="hidden">
          <!-- Report preview will be rendered here -->
        </div>
      </div>
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'O-Level Report Cards',
          'Generate comprehensive student report cards'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot generate report cards without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before generating reports.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-reports', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'OLevelReportCardsComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },



  // Populate dropdowns
  populateDropdowns() {
    // Populate Academic Year dropdown
    const academicYearSelect = document.getElementById('academic_year');
    if (academicYearSelect) {
      // Clear existing options
      academicYearSelect.innerHTML = '<option value="">Select Academic Year</option>';

      if (this.state.academicYears && this.state.academicYears.length > 0) {
        this.state.academicYears.forEach(year => {
          const option = document.createElement('option');
          option.value = year.id;
          option.textContent = year.name;
          academicYearSelect.appendChild(option);
        });

        // Pre-select current academic year
        const currentYear = window.AcademicContext.getActiveAcademicYear();
        if (currentYear) {
          academicYearSelect.value = currentYear.id;
          this.state.selectedAcademicYear = currentYear.id;
        }
      }
    }

    // Initialize disabled dropdowns
    this.initializeDisabledDropdowns();

    // Always update terms dropdown after populating academic year
    this.updateTermsDropdown();
  },

  // Initialize disabled dropdowns with proper placeholders
  initializeDisabledDropdowns() {
    const termSelect = document.getElementById('term');
    if (termSelect) {
      termSelect.innerHTML = '<option value="">Select Academic Year First</option>';
      termSelect.disabled = true;
    }

    const classSelect = document.getElementById('class');
    if (classSelect) {
      // Populate classes but keep disabled until term is selected
      classSelect.innerHTML = '<option value="">Select Term First</option>';

      if (this.state.classes && this.state.classes.length > 0) {
        this.state.classes.forEach(cls => {
          const option = document.createElement('option');
          option.value = cls.id;
          option.textContent = cls.name;
          classSelect.appendChild(option);
        });
      }

      classSelect.disabled = true;
    }
  },

  // Initialize event listeners (like EnterCAScoresComponent)
  initializeEventListeners() {

    // Academic Year change - enables Term dropdown
    const academicYearSelect = document.getElementById('academic_year');
    if (academicYearSelect) {
      academicYearSelect.addEventListener('change', () => {
        this.updateTermsDropdown();
      });
    }

    // Term change - enables Class dropdown
    const termSelect = document.getElementById('term');
    if (termSelect) {
      termSelect.addEventListener('change', () => {
        this.updateClassDropdown();
      });
    }

    // Class change - handles selection
    const classSelect = document.getElementById('class');
    if (classSelect) {
      classSelect.addEventListener('change', () => {
        this.handleClassChange();
      });
    }

    // Student search with debounce for better performance
    const studentSearchInput = document.getElementById('student_search');
    if (studentSearchInput) {
      const debouncedSearch = window.ComponentLifecycleManager?.debounce(() => {
        this.searchStudents();
      }, 300) || (() => this.searchStudents());

      studentSearchInput.addEventListener('input', debouncedSearch);
    }
  },

  // Update terms dropdown when academic year is selected (like EnterCAScoresComponent)
  updateTermsDropdown() {
    const academicYearSelect = document.getElementById('academic_year');
    const termSelect = document.getElementById('term');

    if (!academicYearSelect || !termSelect) {
      console.error('❌ Academic year or term select elements not found');
      return;
    }

    const selectedYear = academicYearSelect.value;
    this.state.selectedAcademicYear = selectedYear;

    if (!selectedYear) {
      // Reset and disable subsequent dropdowns
      termSelect.innerHTML = '<option value="">Select Academic Year First</option>';
      termSelect.disabled = true;
      this.resetClassDropdown();
      return;
    }

    // Enable and populate terms for selected year
    termSelect.innerHTML = '<option value="">Select Term</option>';

    const filteredTerms = this.state.terms.filter(term =>
      term.academic_year_id == selectedYear
    );

    if (filteredTerms.length > 0) {
      filteredTerms.forEach(term => {
        const option = document.createElement('option');
        option.value = term.id;
        option.textContent = term.name;
        termSelect.appendChild(option);
      });
      termSelect.disabled = false;

      // If this is the current academic year, pre-select current term
      const currentYear = window.AcademicContext.getActiveAcademicYear();
      const currentTerm = window.AcademicContext.getActiveTerm();
      if (currentYear && currentTerm && selectedYear == currentYear.id) {
        termSelect.value = currentTerm.id;
        this.state.selectedTerm = currentTerm.id;
        this.updateClassDropdown();
        return; // Don't reset class dropdown if we pre-selected current term
      }
    } else {
      termSelect.innerHTML = '<option value="">No Terms Available</option>';
      termSelect.disabled = true;
    }

    // Only reset class dropdown if we didn't pre-select current term
    this.resetClassDropdown();
  },

  // Update class dropdown when term is selected
  updateClassDropdown() {
    const termSelect = document.getElementById('term');
    const classSelect = document.getElementById('class');

    if (!termSelect || !classSelect) {
      console.error('❌ Term or class select elements not found');
      return;
    }

    const selectedTerm = termSelect.value;
    this.state.selectedTerm = selectedTerm;

    // Always populate classes, but control enabled state
    classSelect.innerHTML = '<option value="">Select Class</option>';

    if (this.state.classes && this.state.classes.length > 0) {
      this.state.classes.forEach(cls => {
        const option = document.createElement('option');
        option.value = cls.id;
        option.textContent = cls.name;
        classSelect.appendChild(option);
      });

      // Enable only if term is selected
      classSelect.disabled = !selectedTerm;
    } else {
      classSelect.innerHTML = '<option value="">No O-Level Classes Available</option>';
      classSelect.disabled = true;
    }
  },

  // Reset class dropdown
  resetClassDropdown() {
    const classSelect = document.getElementById('class');
    if (classSelect) {
      // Populate classes but keep disabled until term is selected
      classSelect.innerHTML = '<option value="">Select Term First</option>';

      if (this.state.classes && this.state.classes.length > 0) {
        this.state.classes.forEach(cls => {
          const option = document.createElement('option');
          option.value = cls.id;
          option.textContent = cls.name;
          classSelect.appendChild(option);
        });
      }

      classSelect.disabled = true;
      this.state.selectedClass = '';
    }
    this.hideStudentsSection();
  },

  // Update student count display
  updateStudentCount() {
    const countElement = document.getElementById('students-count');
    const statusElement = document.getElementById('selection-status');

    if (countElement) {
      const totalStudents = this.state.students.length;
      countElement.textContent = totalStudents > 0 ? `${totalStudents} student${totalStudents !== 1 ? 's' : ''} available` : '';
    }

    if (statusElement) {
      const selectedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
      const selectedCount = selectedCheckboxes.length;
      const totalStudents = this.state.students.length;

      if (totalStudents > 0) {
        statusElement.textContent = `${selectedCount} of ${totalStudents} selected`;
      } else {
        statusElement.textContent = '';
      }
    }
  },

  // Render students list
  renderStudentsList() {
    const container = document.getElementById('students-list');
    if (!container) return;

    if (!this.state.students.length) {
      // Get current filter values to provide contextual messaging
      const academicYear = document.getElementById('academic_year')?.selectedOptions[0]?.text || '';
      const term = document.getElementById('term')?.selectedOptions[0]?.text || '';
      const classFilter = document.getElementById('class')?.selectedOptions[0]?.text || '';
      const searchTerm = document.getElementById('student_search')?.value || '';

      let message = 'No students found for the selected criteria';
      let suggestion = 'Try adjusting your search criteria or check if students are enrolled.';

      // Provide specific messaging based on applied filters
      if (searchTerm) {
        message = `No students found matching "${searchTerm}"`;
        suggestion = `Try a different search term or check the spelling.`;
      } else if (academicYear && term && classFilter) {
        message = `No students found in ${classFilter}`;
        suggestion = `for ${academicYear} - ${term}. Try selecting a different class or check if students are enrolled.`;
      } else if (academicYear && term) {
        message = `No students found`;
        suggestion = `for ${academicYear} - ${term}. Try selecting a specific class or check if students are enrolled for this period.`;
      }

      container.innerHTML = `
        <div class="text-center py-8 text-gray-500">
          <div class="text-gray-400 mb-4">
            ${SRDesignSystem.components.icon('fas fa-users', '4xl', 'gray-300')}
          </div>
          <p class="${SRDesignSystem.responsive.text.lg} font-medium">${message}</p>
          <p class="${SRDesignSystem.responsive.text.sm}">${suggestion}</p>
        </div>
      `;
      this.updateStudentCount();
      return;
    }

    // Get filtered students
    const filteredStudents = this.getFilteredStudents();

    if (filteredStudents.length === 0) {
      container.innerHTML = `
        <div class="text-center py-8 text-gray-500">
          <div class="text-gray-400 mb-4">
            ${SRDesignSystem.components.icon('fas fa-search', '4xl', 'gray-300')}
          </div>
          <p>No students match your search criteria</p>
        </div>
      `;
      this.updateStudentCount();
      return;
    }

    container.innerHTML = filteredStudents.map(student => `
      <label class="flex items-center space-x-3 p-4 hover:bg-white rounded-lg cursor-pointer border border-gray-300 hover:border-primary-300 hover:shadow-sm transition-all duration-200 bg-gray-50">
        <input type="checkbox"
               class="student-checkbox h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500 focus:ring-2 transition-colors"
               value="${student.student_id}"
               onchange="OLevelReportCardsComponent.updateStudentCount()">
        <div class="flex-1">
          <div class="font-semibold text-gray-900 ${SRDesignSystem.responsive.text.base}">
            ${student.first_name} ${student.last_name} (${student.admission_number})
          </div>
        </div>
      </label>
    `).join('');

    this.updateStudentCount();
  },

  // Search students with current filters and search term
  async searchStudents() {
    if (!this.validateSelections()) {
      return;
    }

    const searchTerm = document.getElementById('student_search')?.value.trim() || '';

    // If no search term, just reload all students for the current filters
    if (!searchTerm) {
      await this.loadStudents();
      return;
    }

    try {
      // Use the selected academic context with search parameters
      const requestParams = {
        academic_year_id: this.state.selectedAcademicYear,
        term_id: this.state.selectedTerm,
        level: 'o_level',
        search: searchTerm // Add search parameter
      };

      const response = await window.EnrollmentsAPI.getAcademicStudentsForClass(
        this.state.selectedClass,
        requestParams
      );

      if (response.success && response.data && response.data.length > 0) {
        // Map the API response to ensure consistent field names
        this.state.students = response.data.map(student => ({
          ...student,
          id: student.student_id || student.id
        }));

        this.renderStudentsList();
        this.showStudentsSection();
      } else {
        // Show no results message
        this.state.students = [];
        this.renderStudentsList();
      }

    } catch (error) {
      console.error('Error searching students:', error);
      this.showError('Failed to search students');
    }
  },

  // Get filtered students based on search criteria (for local filtering)
  getFilteredStudents() {
    // Since search now works with API, just return all loaded students
    return [...this.state.students];
  },

  // Filter students list (called by search input)
  filterStudentsList() {
    this.renderStudentsList();
  },

  // Toggle all students selection (only visible/filtered students)
  toggleAllStudents() {
    const selectAllCheckbox = document.getElementById('select-all-students');
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');

    studentCheckboxes.forEach(checkbox => {
      checkbox.checked = selectAllCheckbox.checked;
    });

    this.updateStudentCount();
  },



  // Preview reports
  async previewReports() {
    const selectedStudents = this.getSelectedStudents();
    if (selectedStudents.length === 0) {
      this.showError('Please select at least one student');
      return;
    }

    try {
      SRDesignSystem.forms.setButtonLoading('preview-reports', true);

      // Load first student's report data for preview
      const previewStudent = selectedStudents[0];
      const reportData = await this.loadStudentReportData(previewStudent.student_id);

      // Store preview data for PDF download
      this.currentPreviewData = reportData;

      this.renderReportPreview(reportData);
      this.showReportPreview();

    } catch (error) {
      console.error('❌ Failed to preview reports:', error);
      this.showError('Failed to generate report preview');
    } finally {
      SRDesignSystem.forms.setButtonLoading('preview-reports', false);
    }
  },

  // Generate reports
  async generateReports() {
    const selectedStudents = this.getSelectedStudents();
    if (selectedStudents.length === 0) {
      this.showError('Please select at least one student');
      return;
    }

    try {
      SRDesignSystem.forms.setButtonLoading('generate-reports', true);

      // Generate reports for all selected students
      const reports = [];
      for (const student of selectedStudents) {
        const reportData = await this.loadStudentReportData(student.student_id);
        reports.push(reportData);
      }

      // Generate PDF
      await this.generatePDFReports(reports);

    } catch (error) {
      console.error('❌ Failed to generate reports:', error);
      this.showError('Failed to generate report cards');
    } finally {
      SRDesignSystem.forms.setButtonLoading('generate-reports', false);
    }
  },

  // Get selected students
  getSelectedStudents() {
    const selectedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
    return Array.from(selectedCheckboxes).map(checkbox => {
      const studentId = checkbox.value;
      return this.state.students.find(student => student.student_id == studentId);
    }).filter(Boolean);
  },

  // Load student report data using new comprehensive API
  async loadStudentReportData(studentId) {
    try {
      // Use the selected academic context (can be current or historical)
      const requestData = {
        term_id: this.state.selectedTerm,
        academic_year_id: this.state.selectedAcademicYear,
        class_id: this.state.selectedClass
      };

      // Use the comprehensive report API that includes all assessment types
      const response = await fetch(`${window.SRConfig.getApiUrl(`/o-level-reports/student/${studentId}/comprehensive-report`)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('smartreport_token')}`
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error('Failed to load comprehensive report data');
      }

      const reportData = await response.json();
      if (!reportData.success) {
        throw new Error(reportData.message || 'Failed to load report data');
      }

      // The comprehensive API now includes school settings and academic context
      const enhancedData = reportData.data;

      return enhancedData;
    } catch (error) {
      console.error('❌ Failed to load student report data:', error);
      throw error;
    }
  },

  // Data processing is handled by the comprehensive report API

  handleClassChange() {
    const classSelect = document.getElementById('class');
    this.state.selectedClass = classSelect.value;

    // Clear student search when class changes
    const studentSearchInput = document.getElementById('student_search');
    if (studentSearchInput) {
      studentSearchInput.value = '';
    }

    this.hideStudentsSection();
  },

  // Load students for selected class
  async loadStudents() {
    if (!this.validateSelections()) return;

    try {
      SRDesignSystem.forms.setButtonLoading('load-students', true);

      // Use the selected academic context (can be current or historical)
      const requestParams = {
        academic_year_id: this.state.selectedAcademicYear,
        term_id: this.state.selectedTerm,
        level: 'o_level'
      };

      const response = await window.EnrollmentsAPI.getAcademicStudentsForClass(
        this.state.selectedClass,
        requestParams
      );

      if (response.success && response.data && response.data.length > 0) {
        this.state.students = response.data;

        // Clear any existing search
        const studentSearchInput = document.getElementById('student_search');
        if (studentSearchInput) {
          studentSearchInput.value = '';
        }

        this.renderStudentsList();
        this.showStudentsSection();
      } else {
        this.showError('No students found for the selected criteria');
      }
    } catch (error) {
      console.error('❌ Failed to load students:', error);
      this.showError('Failed to load students. Please try again.');
    } finally {
      SRDesignSystem.forms.setButtonLoading('load-students', false);
    }
  },

  // Validate selections
  validateSelections() {
    // Check if user has made selections (can be current or historical)
    if (!this.state.selectedAcademicYear) {
      this.showError('Please select an academic year');
      return false;
    }

    if (!this.state.selectedTerm) {
      this.showError('Please select a term');
      return false;
    }

    if (!this.state.selectedClass) {
      this.showError('Please select a class');
      return false;
    }

    return true;
  },

  // Show students section
  showStudentsSection() {
    const section = document.getElementById('students-section');
    if (section) {
      section.classList.remove('hidden');
    }
  },

  // Hide students section
  hideStudentsSection() {
    const section = document.getElementById('students-section');
    if (section) {
      section.classList.add('hidden');
    }
    this.hideReportPreview();
  },

  // Hide report preview
  hideReportPreview() {
    const section = document.getElementById('report-preview-section');
    if (section) {
      section.classList.add('hidden');
    }
  },

  // Show success message
  showSuccess(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'success');
    }
  },

  // Show error message
  showError(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'error');
    }
  },

  // Helper methods for new comprehensive table layout
  getSummativeColumnsCount(classExamTypes) {
    // Count the number of exam types configured for this class
    return classExamTypes ? classExamTypes.length + 1 : 3; // +1 for total column
  },

  renderSummativeHeaders(classExamTypes) {
    if (!classExamTypes || classExamTypes.length === 0) {
      // No exam types configured - show message
      return `<th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">No Exams<br>Configured</th>`;
    }

    let headers = '';
    classExamTypes.forEach(examType => {
      headers += `<th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">${examType.short_name}<br>${examType.weight_percentage}%</th>`;
    });

    // Add total column for summative assessment (always out of 80% in two-tier system)
    headers += `<th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">Total<br>(Out of 80%)</th>`;

    return headers;
  },

  renderSubjectRows(subjects, formativeAssessments, summativeAssessments, termAverages, classExamTypes) {
    if (!subjects || subjects.length === 0) return '';

    return subjects.map(subject => {
      const formative = formativeAssessments.find(fa => fa.subject_id === subject.id) || {};
      const summative = summativeAssessments.find(sa => sa.subject_id === subject.id) || {};
      const termAvg = termAverages.find(ta => ta.subject_id === subject.id) || {};

      return `
        <tr>
          <td class="border border-gray-800 px-2 py-1 font-medium text-left">${subject.name}</td>

          <!-- Formative Assessment Columns -->
          <td class="border border-gray-800 px-1 py-1 text-center">${formative.ca1 !== null && formative.ca1 !== undefined ? Number(formative.ca1).toFixed(1) : '-'}</td>
          <td class="border border-gray-800 px-1 py-1 text-center">${formative.ca2 !== null && formative.ca2 !== undefined ? Number(formative.ca2).toFixed(1) : '-'}</td>
          <td class="border border-gray-800 px-1 py-1 text-center">${formative.ca3 !== null && formative.ca3 !== undefined ? Number(formative.ca3).toFixed(1) : '-'}</td>
          <td class="border border-gray-800 px-1 py-1 text-center">${formative.ca4 !== null && formative.ca4 !== undefined ? Number(formative.ca4).toFixed(1) : '-'}</td>
          <td class="border border-gray-800 px-1 py-1 text-center">${formative.ca5 !== null && formative.ca5 !== undefined ? Number(formative.ca5).toFixed(1) : '-'}</td>
          <td class="border border-gray-800 px-1 py-1 text-center">${formative.ca6 !== null && formative.ca6 !== undefined ? Number(formative.ca6).toFixed(1) : '-'}</td>
          <td class="border border-gray-800 px-1 py-1 text-center font-semibold">${formative.average !== null && formative.average !== undefined ? Number(formative.average).toFixed(1) : '-'}</td>
          <td class="border border-gray-800 px-1 py-1 text-center font-semibold">${formative.totalPoints !== null && formative.totalPoints !== undefined ? Number(formative.totalPoints).toFixed(1) : '-'}</td>

          <!-- Summative Assessment Columns (Dynamic) -->
          ${this.renderSummativeColumns(summative, classExamTypes)}

          <!-- Term Average Columns -->
          <td class="border border-gray-800 px-1 py-1 text-center font-bold">${termAvg.totalMark !== null && termAvg.totalMark !== undefined ? Number(termAvg.totalMark).toFixed(1) : '-'}</td>
          <td class="border border-gray-800 px-1 py-1 text-center font-bold">${termAvg.grade || '-'}</td>

          <!-- Teacher Initials Column -->
          <td class="border border-gray-800 px-1 py-1 text-center font-medium">${subject.teacher_initials || '-'}</td>
        </tr>
      `;
    }).join('');
  },

  renderSummativeColumns(summative, classExamTypes) {
    // Render summative assessment columns based on configured exam types from database
    let columns = '';

    // Add exam type scores in the order they were configured
    if (summative.examScores && summative.examScores.length > 0) {
      summative.examScores.forEach(score => {
        columns += `<td class="border border-gray-800 px-1 py-1 text-center">${score !== null && score !== undefined ? Number(score).toFixed(1) : '-'}</td>`;
      });
    } else if (classExamTypes && classExamTypes.length > 0) {
      // Show empty cells for each configured exam type
      classExamTypes.forEach(() => {
        columns += `<td class="border border-gray-800 px-1 py-1 text-center">-</td>`;
      });
    } else {
      // No exam types configured
      columns += `<td class="border border-gray-800 px-1 py-1 text-center">-</td>`;
    }

    // Add total column (summative total out of 80)
    columns += `<td class="border border-gray-800 px-1 py-1 text-center font-semibold">${summative.total !== null && summative.total !== undefined ? Number(summative.total).toFixed(1) : '-'}</td>`;

    return columns;
  },

  renderTotalsRow(overallSummary, classExamTypes) {
    if (!overallSummary) return '<td colspan="15" class="border border-black px-1 py-1 text-center text-black">No data available</td>';

    // Generate empty cells for formative totals (8 columns: CA1-CA6, Average, Total Out of 20%)
    let formativeTotals = '';
    for (let i = 0; i < 8; i++) {
      formativeTotals += '<td class="border border-black px-1 py-1 text-center text-black">-</td>';
    }

    // Generate empty cells for summative totals (exam types + total column)
    const examColumnsCount = classExamTypes ? classExamTypes.length : 0;
    let summativeTotals = '';
    for (let i = 0; i < examColumnsCount + 1; i++) { // +1 for total column
      summativeTotals += '<td class="border border-black px-1 py-1 text-center text-black">-</td>';
    }

    return `
      ${formativeTotals}
      ${summativeTotals}
      <td class="border border-black px-1 py-1 text-center font-bold text-black">${overallSummary.totalMarks !== null && overallSummary.totalMarks !== undefined ? Number(overallSummary.totalMarks).toFixed(1) : '-'}</td>
      <td class="border border-black px-1 py-1 text-center text-black">-</td>
      <td class="border border-black px-1 py-1 text-center text-black">-</td>
    `;
  },

  renderAveragesRow(overallSummary, classExamTypes) {
    if (!overallSummary) return '<td colspan="15" class="border border-black px-1 py-1 text-center text-black">No data available</td>';

    // Generate empty cells for formative averages (8 columns: CA1-CA6, Average, Total Out of 20%)
    let formativeAverages = '';
    for (let i = 0; i < 8; i++) {
      formativeAverages += '<td class="border border-black px-1 py-1 text-center text-black">-</td>';
    }

    // Generate empty cells for summative averages (exam types + total column)
    const examColumnsCount = classExamTypes ? classExamTypes.length : 0;
    let summativeAverages = '';
    for (let i = 0; i < examColumnsCount + 1; i++) { // +1 for total column
      summativeAverages += '<td class="border border-black px-1 py-1 text-center text-black">-</td>';
    }

    return `
      ${formativeAverages}
      ${summativeAverages}
      <td class="border border-black px-1 py-1 text-center font-bold text-black">${overallSummary.average !== null && overallSummary.average !== undefined ? Number(overallSummary.average).toFixed(1) + '%' : '-'}</td>
      <td class="border border-black px-1 py-1 text-center font-bold text-black">${overallSummary.overallGrade || '-'}</td>
      <td class="border border-black px-1 py-1 text-center text-black">-</td>
    `;
  },



  renderGradingScale(gradingScale) {
    if (!gradingScale || gradingScale.length === 0) {
      // No grading scale configured in database
      return `<div class="text-center text-gray-500">No grading scale configured</div>`;
    }

    return gradingScale.map(grade => `
      <div class="flex justify-between">
        <span class="font-semibold">${grade.grade_letter} (${grade.grade_descriptor}):</span>
        <span>${grade.min_percentage}-${grade.max_percentage}%</span>
      </div>
    `).join('');
  },

  // Render grading scale table for page two
  renderGradingScaleTable(gradeBoundaries) {
    if (!gradeBoundaries || gradeBoundaries.length === 0) {
      return `<tr><td colspan="3" class="border border-black p-3 text-center text-black">No grading scale configured</td></tr>`;
    }

    return gradeBoundaries.map(boundary => `
      <tr>
        <td class="border border-black p-3 text-center text-black font-bold">${boundary.grade_letter || '-'}</td>
        <td class="border border-black p-3 text-center text-black">${boundary.min_percentage || 0}-${boundary.max_percentage || 0}%</td>
        <td class="border border-black p-3 text-center text-black">${boundary.grade_descriptor || '-'}</td>
      </tr>
    `).join('');
  },

  // Render competency descriptors table for page two
  renderCompetencyTable(gradingScale) {
    if (!gradingScale || gradingScale.length === 0) {
      return `<tr><td colspan="3" class="border border-black p-3 text-center text-black">No competency descriptors configured</td></tr>`;
    }

    return gradingScale.map(scale => `
      <tr>
        <td class="border border-black p-3 text-center text-black font-bold">${scale.competency_level || '-'}</td>
        <td class="border border-black p-3 text-center text-black">${scale.min_score || 0} - ${scale.max_score || 0}</td>
        <td class="border border-black p-3 text-left text-black">${scale.competency_description || '-'}</td>
      </tr>
    `).join('');
  },

  // Render student competency level row based on calculated average
  renderStudentCompetencyRow(formativeAssessments, subjects, gradingScale) {
    if (!formativeAssessments || !subjects || formativeAssessments.length === 0 || subjects.length === 0) {
      return `<tr><td colspan="2" class="border border-black p-2 text-center text-black text-xs">No assessment data available</td></tr>`;
    }

    // Calculate total average of all CA averages for each subject
    let totalCAAverage = 0;
    let subjectsWithData = 0;

    subjects.forEach(subject => {
      const subjectAssessments = formativeAssessments.filter(fa => fa.subject_id === subject.id);
      if (subjectAssessments.length > 0) {
        const subjectAverage = subjectAssessments.reduce((sum, assessment) => sum + (assessment.average_score || 0), 0) / subjectAssessments.length;
        totalCAAverage += subjectAverage;
        subjectsWithData++;
      }
    });

    const overallCAAverage = subjectsWithData > 0 ? totalCAAverage / subjectsWithData : 0;

    // Find matching competency level
    let matchingCompetency = null;
    if (gradingScale && gradingScale.length > 0) {
      for (const scale of gradingScale) {
        if (overallCAAverage >= (scale.min_score || 0) && overallCAAverage <= (scale.max_score || 0)) {
          matchingCompetency = scale;
          break;
        }
      }
    }

    if (!matchingCompetency) {
      return `<tr><td colspan="2" class="border border-black p-2 text-center text-black text-xs">No matching competency level found</td></tr>`;
    }

    return `
      <tr>
        <td class="border-2 border-black p-4 text-center text-black font-bold text-base">${matchingCompetency.competency_level || '-'}</td>
        <td class="border-2 border-black p-4 text-left text-black text-base pl-6">${matchingCompetency.competency_description || '-'}</td>
      </tr>
    `;
  },

  // Grade calculations are now handled by the comprehensive report API

  // Show report preview
  showReportPreview() {
    const section = document.getElementById('report-preview-section');
    if (section) {
      section.classList.remove('hidden');
      section.scrollIntoView({ behavior: 'smooth' });
    }
  },

  // Render report preview
  renderReportPreview(reportData) {
    const container = document.getElementById('report-preview-section');
    if (!container) return;

    container.innerHTML = `
      <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-100 px-6 py-4 border-b border-blue-200">
          <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-blue-900 flex items-center">
            ${SRDesignSystem.components.icon('fas fa-eye', 'base', 'blue-600')}
            <span class="ml-3">Report Card Preview - ${reportData.student.first_name} ${reportData.student.last_name}</span>
          </h3>
        </div>

        <div class="p-6">
          <div class="mb-4 flex justify-end">
            ${SRDesignSystem.forms.button('download-preview-pdf', 'Download PDF', 'primary', {
              onclick: 'OLevelReportCardsComponent.downloadPreviewPDF()',
              icon: 'fas fa-download'
            })}
          </div>
          ${this.renderReportCard(reportData)}
        </div>
      </div>
    `;
  },

  // Render complete report card with new comprehensive layout
  renderReportCard(reportData) {
    const {
      student,
      subjects,
      formativeAssessments,
      summativeAssessments,
      termAverages,
      classExamTypes,
      schoolSettings,
      academicContext,
      gradingScale,
      overallSummary
    } = reportData;

    return `
      <div class="report-card max-w-6xl mx-auto bg-white min-h-screen" style="font-family: 'Bookman Old Style', 'Times New Roman', serif;">
        <!-- School Name at Top of Page -->
        <div class="text-center mb-6">
          <h1 class="text-4xl font-bold text-black uppercase tracking-wide">${schoolSettings?.school_name || 'SCHOOL NAME'}</h1>
        </div>

        <!-- HEADER SECTION -->
        <div class="mb-8">
          <!-- Top Grid Layout (3 Columns) - No Border -->
          <div class="grid grid-cols-3 gap-8 items-center p-6">
            <!-- Left Column: School Logo -->
            <div class="flex justify-center">
              <img src="${this.getSchoolLogoUrl(schoolSettings?.school_logo)}" alt="School Logo" class="w-24 h-24 object-contain border-2 border-black">
            </div>

            <!-- Middle Column: School Information -->
            <div class="text-center">
              <!-- School Address (1st line) -->
              <p class="text-base text-black mb-2 font-medium">${schoolSettings?.school_address || 'School Address'}</p>

              <!-- Email and Website (2nd line) -->
              <p class="text-sm text-black mb-1">
                Email: ${schoolSettings?.school_email || '<EMAIL>'}, Website: ${schoolSettings?.school_website || 'www.school.ac.ug'}
              </p>

              <!-- Tel (3rd line) -->
              <p class="text-sm text-black">
                Tel: ${schoolSettings?.school_contacts || '+256 xxx xxxxxx'}
              </p>
            </div>

            <!-- Right Column: Student Photo -->
            <div class="flex justify-center">
              ${student.passport_photo ? `
                <img src="${this.getStudentPhotoUrl(student.passport_photo)}" alt="Student Photo" class="w-24 h-28 object-cover border-2 border-black grayscale">
              ` : `
                <div class="w-24 h-28 border-2 border-black flex items-center justify-center text-sm text-black bg-white font-bold">[STUDENT PHOTO]</div>
              `}
            </div>
          </div>
        </div>

        <!-- DOCUMENT TITLE SECTION -->
        <div class="mb-8">
          <!-- Horizontal line separator -->
          <hr class="border-black border-2 mb-4">

          <!-- Report Title centered -->
          <div class="text-center mb-4">
            <h2 class="text-xl font-bold text-black uppercase tracking-wide">END OF ${(academicContext?.term_name || 'TERM').toUpperCase()} ACADEMIC REPORT CARD ${(academicContext?.academic_year_name || 'YEAR').toUpperCase()}</h2>
          </div>

          <!-- Horizontal line separator -->
          <hr class="border-black border-2 mb-4">
        </div>

        <!-- STUDENT INFORMATION SECTION -->
        <div class="bg-white p-6 mb-8 border-2 border-black">
          <div class="grid grid-cols-4 gap-6 mb-4 text-base">
            <span class="font-bold text-black">Student Name:</span>
            <span class="text-black font-medium">${student.first_name} ${student.last_name}</span>
            <span class="font-bold text-black">Admission Number:</span>
            <span class="text-black font-medium">${student.admission_number}</span>
          </div>
          <div class="grid grid-cols-4 gap-6 text-base">
            <span class="font-bold text-black">Class:</span>
            <span class="text-black font-medium">${academicContext?.class_name || '-'}</span>
            <span class="font-bold text-black">Stream:</span>
            <span class="text-black font-medium">${academicContext?.stream_name || '-'}</span>
          </div>
        </div>

        <!-- Comprehensive Assessment Table -->
        <div class="mb-6">
          <div class="overflow-x-auto">
            <table class="w-full border-collapse border border-gray-800 text-xs">
              <thead>
                <tr class="bg-gray-100">
                  <!-- Subject Column -->
                  <th rowspan="2" class="border border-gray-800 px-2 py-2 text-center font-bold align-middle">SUBJECTS</th>

                  <!-- Formative Assessment Section -->
                  <th colspan="8" class="border border-gray-800 px-1 py-1 text-center font-bold bg-blue-50">FORMATIVE ASSESSMENT</th>

                  <!-- Summative Assessment Section -->
                  <th colspan="${this.getSummativeColumnsCount(classExamTypes)}" class="border border-gray-800 px-1 py-1 text-center font-bold bg-green-50">SUMMATIVE ASSESSMENT</th>

                  <!-- Term Average Section -->
                  <th colspan="2" class="border border-gray-800 px-1 py-1 text-center font-bold bg-yellow-50">TERM AVERAGE</th>

                  <!-- Teacher Initials Section -->
                  <th rowspan="2" class="border border-gray-800 px-1 py-1 text-center font-bold bg-purple-50 align-middle">INITIALS</th>
                </tr>
                <tr class="bg-gray-50">
                  <!-- Formative Assessment Sub-headers -->
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">CA 1</th>
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">CA 2</th>
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">CA 3</th>
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">CA 4</th>
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">CA 5</th>
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">CA 6</th>
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">AVE</th>
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">Total<br>(Out of 20%)</th>

                  <!-- Summative Assessment Sub-headers (Dynamic based on class exam types) -->
                  ${this.renderSummativeHeaders(classExamTypes)}

                  <!-- Term Average Sub-headers -->
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">Total Mark<br>(20%) + (80%)</th>
                  <th class="border border-gray-800 px-1 py-1 text-center font-bold text-xs">Grade</th>
                </tr>
              </thead>
              <tbody>
                ${this.renderSubjectRows(subjects, formativeAssessments, summativeAssessments, termAverages, classExamTypes)}

                <!-- Totals Row -->
                <tr class="bg-white font-bold">
                  <td class="border border-black px-2 py-1 text-center text-black">TOTAL</td>
                  ${this.renderTotalsRow(overallSummary, classExamTypes)}
                </tr>

                <!-- Averages Row -->
                <tr class="bg-white font-bold">
                  <td class="border border-black px-2 py-1 text-center text-black">AVERAGE</td>
                  ${this.renderAveragesRow(overallSummary, classExamTypes)}
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Student Competency Level -->
        <div class="mb-10">
          <table class="w-full border-collapse border-2 border-black">
            <thead>
              <tr>
                <th class="border-2 border-black p-4 bg-white font-bold text-black text-base">IDENTIFIER</th>
                <th class="border-2 border-black p-4 bg-white font-bold text-black text-base">DESCRIPTOR</th>
              </tr>
            </thead>
            <tbody>
              ${this.renderStudentCompetencyRow(reportData.formativeAssessments, reportData.subjects, reportData.gradingScale)}
            </tbody>
          </table>
        </div>

        <!-- Comments Section -->
        <div class="mb-10">
          <!-- Class Teacher Comment and Signature on same row -->
          <div class="flex items-center mb-6 text-base">
            <span class="font-bold text-black mr-4 whitespace-nowrap">Class Teacher's Comment:</span>
            <span class="flex-1 text-black mr-4">................................................................................................................................................................................................</span>
            <span class="font-bold text-black mr-4 whitespace-nowrap">Signature:</span>
            <span class="text-black whitespace-nowrap">................................</span>
          </div>

          <!-- Head Teacher Comment and Signature on same row -->
          <div class="flex items-center mb-6 text-base">
            <span class="font-bold text-black mr-4 whitespace-nowrap">Head Teacher's Comment:</span>
            <span class="flex-1 text-black mr-4">................................................................................................................................................................................................</span>
            <span class="font-bold text-black mr-4 whitespace-nowrap">Signature:</span>
            <span class="text-black whitespace-nowrap">................................</span>
          </div>
        </div>

        <!-- Administrative Information -->
        <div class="mb-10">
          <div class="flex justify-between items-center text-base font-bold text-black py-4">
            <span>Next Term Begins on: ${reportData.nextTerm ? new Date(reportData.nextTerm.start_date).toLocaleDateString() : 'TBD'}</span>
            <span>Next Term Ends on: ${reportData.nextTerm ? new Date(reportData.nextTerm.end_date).toLocaleDateString() : 'TBD'}</span>
          </div>
        </div>

        <!-- Document Footer -->
        <div class="mt-6 text-right">
          <span class="text-sm font-bold text-black">Date Printed: ${new Date().toLocaleDateString()}</span>
        </div>

        <!-- School Motto Footer -->
        ${schoolSettings?.school_motto ? `
          <div class="text-center mt-8 pt-4 border-t border-black">
            <p class="text-sm text-black italic">"${schoolSettings.school_motto.charAt(0).toUpperCase() + schoolSettings.school_motto.slice(1).toLowerCase()}"</p>
          </div>
        ` : ''}
      </div>

      <!-- PAGE TWO: COMPETENCY DESCRIPTORS AND GRADING SCALE -->
      <div class="page-break" style="page-break-before: always; break-before: page;"></div>
      <div class="report-card max-w-6xl mx-auto bg-white mt-8" style="font-family: 'Bookman Old Style', 'Times New Roman', serif;">

        <!-- Competency Descriptors Table (First) -->
        <div class="mb-8">
          <h3 class="text-base font-bold text-black mb-4">COMPETENCY DESCRIPTORS</h3>
          <table class="w-full border-collapse border border-black">
            <thead>
              <tr>
                <th class="border border-black p-3 bg-white font-bold text-black">IDENTIFIER</th>
                <th class="border border-black p-3 bg-white font-bold text-black">SCORE RANGE</th>
                <th class="border border-black p-3 bg-white font-bold text-black">DESCRIPTOR</th>
              </tr>
            </thead>
            <tbody>
              ${this.renderCompetencyTable(reportData.gradingScale)}
            </tbody>
          </table>
        </div>

        <!-- Grading Scale Table (Second) -->
        <div class="mb-8">
          <h3 class="text-base font-bold text-black mb-4">GRADING SCALE</h3>
          <table class="w-full border-collapse border border-black">
            <thead>
              <tr>
                <th class="border border-black p-3 bg-white font-bold text-black">GRADE</th>
                <th class="border border-black p-3 bg-white font-bold text-black">RANGE</th>
                <th class="border border-black p-3 bg-white font-bold text-black">DESCRIPTOR</th>
              </tr>
            </thead>
            <tbody>
              ${this.renderGradingScaleTable(reportData.gradeBoundaries)}
            </tbody>
          </table>
        </div>
      </div>
    `;
  },

  // Generate PDF reports using server-side Puppeteer
  async generatePDFReports(reports) {
    try {
      console.log('📄 Generating PDF reports for', reports.length, 'students');

      // Check if user is authenticated
      const token = localStorage.getItem('smartreport_token');
      if (!token) {
        throw new Error('No authentication token found. Please login again.');
      }

      // Send request to server for PDF generation
      const response = await fetch(`${window.SRConfig.getApiUrl('/o-level-reports/pdf/o-level/bulk')}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('smartreport_token')}`
        },
        body: JSON.stringify({
          reportsData: reports,
          academicContext: {
            class_name: this.state.classes.find(c => c.id == this.state.selectedClass)?.name || 'Class',
            academic_year_name: this.state.academicYears.find(y => y.id == this.state.selectedAcademicYear)?.name || 'Academic Year',
            term_name: this.state.terms.find(t => t.id == this.state.selectedTerm)?.name || 'Term'
          },
          totalStudentsInClass: this.state.students.length
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate PDF');
      }

      const result = await response.json();
      if (result.success) {
        console.log('✅ PDF reports generated successfully');
        console.log(`📄 Files generated: ${result.filesGenerated}`);

        let message;
        if (result.createdFolder && result.folderName) {
          console.log(`📁 Folder created: ${result.folderName}`);
          message = `Successfully generated ${result.filesGenerated} report cards in folder: ${result.folderName}`;
        } else {
          console.log('📄 Individual PDFs saved to Downloads');
          message = `Successfully generated ${result.filesGenerated} individual report card${result.filesGenerated > 1 ? 's' : ''} in Downloads folder`;
        }

        // Show success message to user
        SRDesignSystem.notifications.show(message, 'success');
      } else {
        throw new Error(result.message || 'Failed to generate bulk PDF report cards');
      }

      return true;
    } catch (error) {
      console.error('❌ PDF generation failed:', error);
      throw error;
    }
  },

  // Download PDF for the currently previewed report
  async downloadPreviewPDF() {
    if (!this.currentPreviewData) {
      this.showError('No report data available for download');
      return;
    }

    try {
      SRDesignSystem.forms.setButtonLoading('download-preview-pdf', true);

      // Check if user is authenticated
      const token = localStorage.getItem('smartreport_token');
      if (!token) {
        throw new Error('No authentication token found. Please login again.');
      }

      // Send request to server for single PDF generation
      const response = await fetch(`${window.SRConfig.getApiUrl('/o-level-reports/pdf/o-level/single')}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('smartreport_token')}`
        },
        body: JSON.stringify({
          reportData: this.currentPreviewData,
          academicContext: {
            academic_year_name: this.state.academicYears.find(y => y.id == this.state.selectedAcademicYear)?.name || 'Academic Year',
            term_name: this.state.terms.find(t => t.id == this.state.selectedTerm)?.name || 'Term'
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate PDF');
      }

      const result = await response.json();
      if (result.success) {
        console.log('✅ Single PDF generated successfully');
        console.log(`📄 File saved: ${result.fileName}`);

        // Show success message to user
        SRDesignSystem.notifications.show(
          `Report card saved successfully: ${result.fileName}`,
          'success'
        );
      } else {
        throw new Error(result.message || 'Failed to generate PDF report card');
      }

      this.showSuccess('PDF downloaded successfully');

    } catch (error) {
      console.error('❌ Preview PDF download failed:', error);
      this.showError('Failed to download PDF');
    } finally {
      SRDesignSystem.forms.setButtonLoading('download-preview-pdf', false);
    }
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  },

  // Utility methods for image URL handling
  // Get school logo URL for display
  getSchoolLogoUrl(logoPath) {
    if (!logoPath) {
      return `${window.SR.serverUrl}/assets/images/default-school-logo.png`;
    }

    // If it's already a full URL, return as is
    if (logoPath.startsWith('http://') || logoPath.startsWith('https://')) {
      return logoPath;
    }

    // If it starts with a slash, it's relative to server root
    if (logoPath.startsWith('/')) {
      return `${window.SR.serverUrl}${logoPath}`;
    }

    // Otherwise, assume it's relative to the school uploads directory
    return `${window.SR.serverUrl}/assets/images/uploads/school/${logoPath}`;
  },

  // Get student photo URL for display
  getStudentPhotoUrl(photoPath) {
    if (!photoPath) {
      return null;
    }

    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }

    // If it starts with a slash, it's relative to server root
    if (photoPath.startsWith('/')) {
      return `${window.SR.serverUrl}${photoPath}`;
    }

    // Otherwise, assume it's relative to the o-level students uploads directory
    return `${window.SR.serverUrl}/assets/images/uploads/o-level-students/${photoPath}`;
  }
};

// Export to global scope
window.OLevelReportCardsComponent = OLevelReportCardsComponent;
