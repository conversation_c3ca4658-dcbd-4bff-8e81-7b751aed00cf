const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;

class PDFOLevelReportGenerator {
  constructor() {
    this.browser = null;
  }

  // Initialize browser instance
  async initBrowser() {
    if (!this.browser) {
      try {
        console.log('Initializing browser for PDF generation...');

        // Use puppeteer's bundled Chromium for reliability
        this.browser = await puppeteer.launch({
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--allow-running-insecure-content',
            '--disable-features=VizDisplayCompositor'
          ]
        });

        console.log('Browser initialized successfully');
      } catch (error) {
        console.error('Failed to launch browser:', error);
        throw new Error(`PDF generation service unavailable: ${error.message}`);
      }
    }
    return this.browser;
  }

  // Close browser instance
  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  // Get Downloads directory path
  getDownloadsPath() {
    try {
      // Try Electron API first (for packaged app)
      const { app } = require('electron');
      return app.getPath('downloads');
    } catch (error) {
      // Fallback for development mode
      console.log('Electron not available, using fallback Downloads path');
      const os = require('os');
      return path.join(os.homedir(), 'Downloads');
    }
  }

  // Get the default school logo path
  getDefaultLogoPath() {
    // Use server URL for consistency with frontend
    return 'http://localhost:3001/assets/images/default-school-logo.png';
  }

  // Get school logo URL for PDF generation
  getSchoolLogoUrl(logoPath) {
    if (!logoPath) {
      return this.getDefaultLogoPath();
    }

    // If it's already a full URL, return as is
    if (logoPath.startsWith('http://') || logoPath.startsWith('https://')) {
      return logoPath;
    }

    // If it starts with a slash, it's relative to server root
    if (logoPath.startsWith('/')) {
      return `http://localhost:3001${logoPath}`;
    }

    // Otherwise, assume it's relative to the school uploads directory
    return `http://localhost:3001/assets/images/uploads/school/${logoPath}`;
  }

  // Get student photo URL for PDF generation
  getStudentPhotoUrl(photoPath) {
    if (!photoPath) {
      return null;
    }

    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }

    // If it starts with a slash, it's relative to server root
    if (photoPath.startsWith('/')) {
      return `http://localhost:3001${photoPath}`;
    }

    // Otherwise, assume it's relative to the o-level students uploads directory
    return `http://localhost:3001/assets/images/uploads/o-level-students/${photoPath}`;
  }

  // Create unique directory name to avoid conflicts
  async createUniqueDirectory(basePath, baseName) {
    let dirName = baseName;
    let counter = 1;
    let fullPath = path.join(basePath, dirName);

    // Check if directory exists and create unique name if needed
    while (true) {
      try {
        await fs.access(fullPath);
        // Directory exists, try with counter
        dirName = `${baseName} (${counter})`;
        fullPath = path.join(basePath, dirName);
        counter++;
      } catch (error) {
        // Directory doesn't exist, we can use this name
        break;
      }
    }

    // Create the directory
    await fs.mkdir(fullPath, { recursive: true });
    return fullPath;
  }

  // Create unique file name to avoid conflicts
  async createUniqueFileName(dirPath, baseName, extension) {
    let fileName = `${baseName}.${extension}`;
    let counter = 1;
    let fullPath = path.join(dirPath, fileName);

    // Check if file exists and create unique name if needed
    while (true) {
      try {
        await fs.access(fullPath);
        // File exists, try with counter
        fileName = `${baseName} (${counter}).${extension}`;
        fullPath = path.join(dirPath, fileName);
        counter++;
      } catch (error) {
        // File doesn't exist, we can use this name
        break;
      }
    }

    return { fileName, fullPath };
  }

  // Generate O-Level report card PDF and save to Downloads
  async generateOLevelReportCard(reportData, academicContext) {
    console.log('🚀 Starting single PDF generation...');
    let page;

    try {
      const downloadsPath = this.getDownloadsPath();

      const browser = await this.initBrowser();
      page = await browser.newPage();

      // Set page size and margins for report card
      await page.setViewport({ width: 1200, height: 1600 });

      // Generate HTML content for the report card
      const htmlContent = this.generateReportCardHTML(reportData);

      // Set the HTML content
      await page.setContent(htmlContent, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Generate PDF with compact options for single page
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: false, // No background colors for black and white
        margin: {
          top: '0.3in',
          right: '0.3in',
          bottom: '0.3in',
          left: '0.3in'
        },
        displayHeaderFooter: false,
        preferCSSPageSize: true
      });

      // Save to Downloads directory
      const student = reportData.student;
      const fileName = `${student.first_name}_${student.last_name}_${academicContext.academic_year_name}_${academicContext.term_name}`;

      const { fullPath } = await this.createUniqueFileName(downloadsPath, fileName, 'pdf');
      await fs.writeFile(fullPath, pdfBuffer);

      console.log('✅ PDF saved successfully:', fullPath);
      return {
        success: true,
        filePath: fullPath,
        fileName: path.basename(fullPath)
      };

    } catch (error) {
      console.error('❌ PDF generation error:', error);
      console.error('❌ Error stack:', error.stack);
      throw new Error(`Failed to generate PDF report card: ${error.message}`);
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  // Generate multiple report cards and save to Downloads folder
  async generateBulkOLevelReportCards(reportsData, academicContext, shouldCreateFolder = true) {
    console.log('🚀 Starting bulk PDF generation for', reportsData.length, 'students...');
    let page;

    try {
      const downloadsPath = this.getDownloadsPath();

      const browser = await this.initBrowser();
      page = await browser.newPage();
      await page.setViewport({ width: 1200, height: 1600 });

      let folderPath = downloadsPath;
      let folderName = null;

      if (shouldCreateFolder) {
        folderName = `${academicContext.class_name} Report Cards ${academicContext.academic_year_name}_${academicContext.term_name}`;
        folderPath = await this.createUniqueDirectory(downloadsPath, folderName);
        console.log('📂 Created folder:', folderPath);
      }

      // Save individual PDFs for each student
      const savedFiles = [];
      for (let i = 0; i < reportsData.length; i++) {
        const reportData = reportsData[i];
        const student = reportData.student;

        console.log(`📄 Generating PDF ${i + 1}/${reportsData.length} for ${student.first_name} ${student.last_name}...`);

        try {
          // Generate individual PDF using page
          const htmlContent = this.generateReportCardHTML(reportData);

          await page.setContent(htmlContent, {
            waitUntil: 'networkidle0',
            timeout: 30000
          });

          const pdfBuffer = await page.pdf({
            format: 'A4',
            printBackground: false, // No background colors for black and white
            margin: {
              top: '0.3in',
              right: '0.3in',
              bottom: '0.3in',
              left: '0.3in'
            },
            displayHeaderFooter: false,
            preferCSSPageSize: true
          });

          const fileName = `${student.first_name}_${student.last_name}_Report_Card`;
          const { fullPath } = await this.createUniqueFileName(folderPath, fileName, 'pdf');

          await fs.writeFile(fullPath, pdfBuffer);

          savedFiles.push({
            studentName: `${student.first_name} ${student.last_name}`,
            filePath: fullPath,
            fileName: path.basename(fullPath)
          });

          console.log(`✅ Generated PDF for ${student.first_name} ${student.last_name}`);
        } catch (studentError) {
          console.error(`❌ Failed to generate PDF for ${student.first_name} ${student.last_name}:`, studentError.message);
          // Continue with other students
        }
      }

      console.log(`🎉 Bulk PDF generation completed. Generated ${savedFiles.length}/${reportsData.length} files.`);

      return {
        success: true,
        folderPath: shouldCreateFolder ? folderPath : downloadsPath,
        folderName: shouldCreateFolder ? path.basename(folderPath) : null,
        filesGenerated: savedFiles.length,
        files: savedFiles
      };

    } catch (error) {
      console.error('❌ Bulk PDF generation error:', error);
      console.error('❌ Error stack:', error.stack);
      throw new Error(`Failed to generate bulk PDF report cards: ${error.message}`);
    } finally {
      if (page) {
        await page.close();
      }
    }
  }



  // Generate HTML content for a single report card
  generateReportCardHTML(reportData) {
    if (!reportData) {
      throw new Error('Report data is required');
    }

    const { student = {} } = reportData;

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report Card - ${student.first_name || 'Student'} ${student.last_name || ''}</title>
        <style>
          ${this.getReportCardCSS()}
        </style>
      </head>
      <body>
        <div class="report-card">
          ${this.generateComprehensiveReportCardContent(reportData)}
        </div>
      </body>
      </html>
    `;
  }

  // Generate comprehensive report card content with new layout
  generateComprehensiveReportCardContent(reportData) {
    const {
      student = {},
      subjects = [],
      formativeAssessments = [],
      summativeAssessments = [],
      termAverages = [],
      classExamTypes = [],
      schoolSettings = {},
      academicContext = {},
      gradingScale = [],
      overallSummary = {}
    } = reportData || {};

    return `
      <!-- School Name at Top of Page -->
      <div class="page-header">
        <h1 class="school-name-top">${schoolSettings?.school_name ? schoolSettings.school_name.toUpperCase() : 'SCHOOL NAME'}</h1>
      </div>

      <!-- HEADER SECTION -->
      <div class="header">
        <!-- Top Grid Layout (3 Columns) - No Border -->
        <div class="header-grid">
          <!-- Left Column: School Logo -->
          <div class="header-logo">
            <img src="${this.getSchoolLogoUrl(schoolSettings?.school_logo)}" alt="School Logo" class="school-logo">
          </div>

          <!-- Middle Column: School Information -->
          <div class="header-middle">
            <!-- School Address (1st line) -->
            <p class="school-address">${schoolSettings?.school_address || 'School Address'}</p>

            <!-- Email and Website (2nd line) -->
            <p class="school-contacts">
              Email: ${schoolSettings?.school_email || '<EMAIL>'}, Website: ${schoolSettings?.school_website || 'www.school.ac.ug'}
            </p>

            <!-- Tel (3rd line) -->
            <p class="school-contacts">
              Tel: ${schoolSettings?.school_contacts || '+256 xxx xxxxxx'}
            </p>
          </div>

          <!-- Right Column: Student Photo -->
          <div class="header-photo">
            ${student.passport_photo ? `
              <img src="${this.getStudentPhotoUrl(student.passport_photo)}" alt="Student Photo" class="student-photo">
            ` : `
              <div class="photo-placeholder">[STUDENT PHOTO]</div>
            `}
          </div>
        </div>
      </div>

      <!-- DOCUMENT TITLE SECTION -->
      <div class="document-title-section">
        <!-- Horizontal line separator -->
        <hr class="title-separator">

        <!-- Report title centered -->
        <div class="report-title">
          <h2>END OF ${(academicContext?.term_name || 'TERM').toUpperCase()} ACADEMIC REPORT CARD ${(academicContext?.academic_year_name || 'YEAR').toUpperCase()}</h2>
        </div>

        <!-- Horizontal line separator -->
        <hr class="title-separator">
      </div>

      <!-- STUDENT INFORMATION SECTION -->
      <div class="student-information-section">
        <div class="student-details">
          <div class="info-row">
            <span class="label">Student Name:</span>
            <span class="value">${student.first_name || '-'} ${student.last_name || '-'}</span>
            <span class="label">Admission Number:</span>
            <span class="value">${student.admission_number || '-'}</span>
          </div>
          <div class="info-row">
            <span class="label">Class:</span>
            <span class="value">${academicContext?.class_name || '-'}</span>
            <span class="label">Stream:</span>
            <span class="value">${academicContext?.stream_name || '-'}</span>
          </div>
        </div>
      </div>

      <!-- Comprehensive Assessment Table -->
      <div class="assessment-section">
        <table class="assessment-table">
          <thead>
            <tr class="main-header">
              <th rowspan="2" class="subject-header">SUBJECTS</th>
              <th colspan="8" class="formative-header">FORMATIVE ASSESSMENT</th>
              <th colspan="${this.getSummativeColumnsCount(classExamTypes)}" class="summative-header">SUMMATIVE ASSESSMENT</th>
              <th colspan="2" class="term-header">TERM AVERAGE</th>
              <th rowspan="2" class="initials-header">INITIALS</th>
            </tr>
            <tr class="sub-header">
              <!-- Formative Assessment Sub-headers -->
              <th>CA 1</th>
              <th>CA 2</th>
              <th>CA 3</th>
              <th>CA 4</th>
              <th>CA 5</th>
              <th>CA 6</th>
              <th>AVE</th>
              <th>Total<br>(Out of 20%)</th>

              <!-- Summative Assessment Sub-headers -->
              ${this.renderSummativeHeaders(classExamTypes)}

              <!-- Term Average Sub-headers -->
              <th>Total Mark<br>(20%) + (80%)</th>
              <th>Grade</th>
            </tr>
          </thead>
          <tbody>
            ${this.renderSubjectRows(subjects, formativeAssessments, summativeAssessments, termAverages, classExamTypes)}

            <!-- Totals and Averages Row -->
            <tr class="totals-row">
              <td class="totals-label">TOTALS/AVERAGES</td>
              ${this.renderTotalsRow(overallSummary, classExamTypes)}
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Grading Scale and Performance Summary -->
      <div class="summary-section">
        <div class="grading-scale">
          <h3>GRADING SCALE</h3>
          <div class="scale-content">
            ${this.renderGradingScale(gradingScale)}
          </div>
        </div>

        <div class="performance-summary">
          <h3>PERFORMANCE SUMMARY</h3>
          <div class="summary-content">
            <div class="summary-item"><span class="label">Subjects Taken:</span> <span>${overallSummary.subjectsTaken || 0}</span></div>
            <div class="summary-item"><span class="label">Total Marks:</span> <span>${overallSummary.totalMarks || 0}/${overallSummary.maxMarks || 0}</span></div>
            <div class="summary-item"><span class="label">Average:</span> <span class="highlight">${overallSummary.average || 0}%</span></div>
            <div class="summary-item"><span class="label">Overall Grade:</span> <span class="grade-highlight">${overallSummary.overallGrade || '-'}</span></div>
          </div>
        </div>
      </div>

      <!-- Comments Section -->
      <div class="comments-section">
        <h3>TEACHER COMMENTS</h3>

        <!-- Class Teacher Comment and Signature on same row -->
        <div class="comment-signature-row">
          <span class="comment-label">Class Teacher's Comment:</span>
          <span class="comment-dots">................................................................................................................................................................................................</span>
          <span class="signature-label">Signature:</span>
          <span class="signature-dots">................................</span>
        </div>

        <!-- Head Teacher Comment and Signature on same row -->
        <div class="comment-signature-row">
          <span class="comment-label">Head Teacher's Comment:</span>
          <span class="comment-dots">................................................................................................................................................................................................</span>
          <span class="signature-label">Signature:</span>
          <span class="signature-dots">................................</span>
        </div>
      </div>

      <!-- Administrative Information -->
      <div class="admin-section">
        <div class="term-dates-row">
          <span class="term-date-left">Next Term Begins on: ${reportData.nextTerm ? new Date(reportData.nextTerm.start_date).toLocaleDateString() : 'TBD'}</span>
          <span class="term-date-right">Next Term Ends on: ${reportData.nextTerm ? new Date(reportData.nextTerm.end_date).toLocaleDateString() : 'TBD'}</span>
        </div>
      </div>

      <!-- School Motto Footer -->
      ${schoolSettings?.school_motto ? `
        <div class="school-motto-footer">
          <p>"${schoolSettings.school_motto.charAt(0).toUpperCase() + schoolSettings.school_motto.slice(1).toLowerCase()}"</p>
        </div>
      ` : ''}

      <!-- Document Footer -->
      <div class="document-footer">
        <span class="date-printed">Date Printed: ${new Date().toLocaleDateString()}</span>
      </div>
    `;
  }

  // Helper methods for table rendering
  getSummativeColumnsCount(classExamTypes) {
    return classExamTypes ? classExamTypes.length + 1 : 3; // +1 for total column
  }

  renderSummativeHeaders(classExamTypes) {
    if (!classExamTypes || classExamTypes.length === 0) {
      return `<th>No Exams<br>Configured</th>`;
    }

    let headers = '';
    classExamTypes.forEach(examType => {
      headers += `<th>${examType.short_name}<br>${examType.weight_percentage}%</th>`;
    });

    // Summative assessment is always out of 80 in the two-tier system
    headers += `<th>Total<br>(Out of 80%)</th>`;

    return headers;
  }

  renderSubjectRows(subjects, formativeAssessments, summativeAssessments, termAverages, classExamTypes) {
    if (!subjects || subjects.length === 0) return '';

    return subjects.map(subject => {
      const formative = formativeAssessments.find(fa => fa.subject_id === subject.id) || {};
      const summative = summativeAssessments.find(sa => sa.subject_id === subject.id) || {};
      const termAvg = termAverages.find(ta => ta.subject_id === subject.id) || {};

      return `
        <tr class="subject-row">
          <td class="subject-name">${subject.name || '-'}</td>

          <!-- Formative Assessment Columns -->
          <td class="ca-score">${formative.ca1 !== null && formative.ca1 !== undefined ? Number(formative.ca1).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca2 !== null && formative.ca2 !== undefined ? Number(formative.ca2).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca3 !== null && formative.ca3 !== undefined ? Number(formative.ca3).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca4 !== null && formative.ca4 !== undefined ? Number(formative.ca4).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca5 !== null && formative.ca5 !== undefined ? Number(formative.ca5).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca6 !== null && formative.ca6 !== undefined ? Number(formative.ca6).toFixed(1) : '-'}</td>
          <td class="ca-average">${formative.average !== null && formative.average !== undefined ? Number(formative.average).toFixed(1) : '-'}</td>
          <td class="ca-points">${formative.totalPoints !== null && formative.totalPoints !== undefined ? Number(formative.totalPoints).toFixed(1) : '-'}</td>

          <!-- Summative Assessment Columns -->
          ${this.renderSummativeColumns(summative, classExamTypes)}

          <!-- Term Average Columns -->
          <td class="total-mark">${termAvg.totalMark !== null && termAvg.totalMark !== undefined ? Number(termAvg.totalMark).toFixed(1) : '-'}</td>
          <td class="grade">${termAvg.grade || '-'}</td>

          <!-- Teacher Initials Column -->
          <td class="teacher-initials">${subject.teacher_initials || '-'}</td>
        </tr>
      `;
    }).join('');
  }

  renderSummativeColumns(summative, classExamTypes) {
    let columns = '';

    if (summative.examScores && summative.examScores.length > 0) {
      summative.examScores.forEach(score => {
        columns += `<td class="exam-score">${score !== null && score !== undefined ? Number(score).toFixed(1) : '-'}</td>`;
      });
    } else if (classExamTypes && classExamTypes.length > 0) {
      // Show empty cells for each configured exam type
      classExamTypes.forEach(() => {
        columns += `<td class="exam-score">-</td>`;
      });
    } else {
      // No exam types configured
      columns += `<td class="exam-score">-</td>`;
    }

    // Add total column
    columns += `<td class="summative-total">${summative.total !== null && summative.total !== undefined ? Number(summative.total).toFixed(1) : '-'}</td>`;

    return columns;
  }

  renderTotalsRow(overallSummary, classExamTypes) {
    const examColumnsCount = classExamTypes ? classExamTypes.length : 2;

    // Generate empty cells for formative totals (8 columns)
    let formativeTotals = '';
    for (let i = 0; i < 8; i++) {
      formativeTotals += '<td class="total-cell">-</td>';
    }

    // Generate empty cells for summative totals
    let summativeTotals = '';
    for (let i = 0; i < examColumnsCount + 1; i++) { // +1 for total column
      summativeTotals += '<td class="total-cell">-</td>';
    }

    return `
      ${formativeTotals}
      ${summativeTotals}
      <td class="overall-total">${overallSummary.totalMarks !== null && overallSummary.totalMarks !== undefined ? Number(overallSummary.totalMarks).toFixed(1) : '-'}</td>
      <td class="overall-grade">${overallSummary.overallGrade || '-'}</td>
      <td class="overall-initials">-</td>
    `;
  }

  renderGradingScale(gradingScale) {
    if (!gradingScale || gradingScale.length === 0) {
      return `<div class="scale-item">No grading scale configured</div>`;
    }

    return gradingScale.map(grade => `
      <div class="scale-item">
        <span class="grade-label">${grade.grade_letter} (${grade.grade_descriptor}):</span>
        <span>${grade.min_percentage}-${grade.max_percentage}%</span>
      </div>
    `).join('');
  }

  // Generate CSS styles for the comprehensive report card
  getReportCardCSS() {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Times New Roman', serif;
        font-size: 12px;
        line-height: 1.3;
        color: #000;
        background: white;
      }

      .report-card {
        max-width: 210mm;
        margin: 0 auto;
        padding: 5mm;
        background: white;
        position: relative;
      }

      /* PAGE HEADER STYLES */
      .page-header {
        text-align: center;
        margin-bottom: 10px;
      }

      .school-name-top {
        font-size: 24px;
        font-weight: bold;
        color: #000;
        text-transform: uppercase;
        margin: 0;
      }

      /* HEADER SECTION STYLES */
      .header {
        margin-bottom: 10px;
      }

      /* Top Grid Layout (3 Columns) - No Border */
      .header-grid {
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        gap: 10px;
        align-items: center;
        padding: 8px;
      }

      /* Left Column: School Logo */
      .header-logo {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .school-logo {
        width: 60px;
        height: 60px;
        object-fit: contain;
        border: 1px solid #000;
      }

      /* Middle Column: School Information */
      .header-middle {
        text-align: center;
      }

      .school-address {
        font-size: 11px;
        margin-bottom: 2px;
        color: #000;
      }

      .school-contacts {
        font-size: 10px;
        color: #000;
      }

      /* Right Column: Student Photo */
      .header-photo {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .student-photo {
        width: 60px;
        height: 75px;
        object-fit: cover;
        border: 1px solid #000;
        filter: grayscale(100%);
      }

      .photo-placeholder {
        width: 60px;
        height: 75px;
        border: 1px solid #000;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        color: #000;
        background-color: #fff;
      }

      /* DOCUMENT TITLE SECTION STYLES */
      .document-title-section {
        margin: 8px 0;
      }

      .title-separator {
        border: none;
        border-top: 1px solid #000;
        margin: 5px 0;
      }

      .report-title {
        text-align: center;
        margin: 8px 0;
      }

      .report-title h2 {
        font-size: 14px;
        font-weight: bold;
        color: #000;
        text-transform: uppercase;
        margin: 0;
      }

      /* STUDENT INFORMATION SECTION STYLES */
      .student-information-section {
        margin-bottom: 10px;
        background-color: #fff;
        padding: 8px;
        border: 1px solid #000;
      }

      .student-details {
        width: 100%;
      }

      .info-row {
        display: grid;
        grid-template-columns: auto 1fr auto 1fr;
        gap: 10px;
        margin-bottom: 4px;
        font-size: 10px;
        align-items: center;
      }

      .info-row .label {
        font-weight: bold;
        color: #000;
      }

      .info-row .value {
        color: #000;
      }

      .photo-placeholder {
        font-size: 10px;
        color: #666;
        text-align: center;
      }

      /* Assessment Table Styles */
      .assessment-section {
        margin-bottom: 10px;
      }

      .assessment-section h3 {
        font-size: 12px;
        font-weight: bold;
        border-bottom: 1px solid #000;
        margin-bottom: 5px;
        padding-bottom: 2px;
        color: #000;
      }

      .assessment-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 10px;
        margin-bottom: 8px;
      }

      .assessment-table th,
      .assessment-table td {
        border: 1px solid #000;
        padding: 3px 2px;
        text-align: center;
        vertical-align: middle;
        color: #000;
        background-color: #fff;
      }

      .main-header th {
        background-color: #fff;
        font-weight: bold;
        font-size: 9px;
        color: #000;
      }

      .sub-header th {
        background-color: #fff;
        font-weight: bold;
        font-size: 8px;
        color: #000;
      }

      .subject-header {
        background-color: #fff !important;
        font-weight: bold;
        width: 120px;
        color: #000;
      }

      .formative-header {
        background-color: #fff !important;
        color: #000;
      }

      .summative-header {
        background-color: #fff !important;
        color: #000;
      }

      .term-header {
        background-color: #fff !important;
        color: #000;
      }

      .initials-header {
        background-color: #fff !important;
        color: #000;
      }

      .subject-row td {
        font-size: 9px;
      }

      .subject-name {
        text-align: left !important;
        font-weight: 500;
        padding-left: 8px !important;
      }

      .ca-score,
      .exam-score {
        font-size: 9px;
      }

      .ca-average,
      .ca-points,
      .summative-total,
      .total-mark,
      .grade {
        font-weight: 600;
      }

      .teacher-initials {
        font-weight: 500;
        font-size: 9px;
      }

      .totals-row {
        background-color: #fff;
        font-weight: bold;
        color: #000;
      }

      .totals-label {
        text-align: center !important;
        font-weight: bold;
        color: #000;
      }

      /* Summary Section Styles */
      .summary-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 10px;
      }

      .grading-scale h3,
      .performance-summary h3 {
        font-size: 12px;
        font-weight: bold;
        border-bottom: 1px solid #000;
        margin-bottom: 5px;
        padding-bottom: 2px;
        color: #000;
      }

      .scale-content,
      .summary-content {
        font-size: 10px;
        color: #000;
      }

      .scale-item,
      .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2px;
      }

      .grade-label,
      .summary-item .label {
        font-weight: 600;
        color: #000;
      }

      .highlight {
        font-weight: bold;
        font-size: 12px;
      }

      .grade-highlight {
        font-weight: bold;
        font-size: 14px;
      }

      /* Comments Section Styles */
      .comments-section {
        margin-bottom: 10px;
      }

      .comments-section h3 {
        font-size: 12px;
        font-weight: bold;
        border-bottom: 1px solid #000;
        margin-bottom: 8px;
        padding-bottom: 2px;
        color: #000;
      }

      .comment-signature-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 10px;
      }

      .comment-label {
        font-weight: 600;
        color: #000;
        margin-right: 8px;
        white-space: nowrap;
      }

      .comment-dots {
        flex: 1;
        color: #000;
        margin-right: 8px;
      }

      .signature-label {
        font-weight: 600;
        color: #000;
        margin-right: 8px;
        white-space: nowrap;
      }

      .signature-dots {
        color: #000;
        white-space: nowrap;
      }

      /* Administrative Section Styles */
      .admin-section {
        margin-bottom: 15px;
      }

      .term-dates-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 11px;
        color: #000;
        font-weight: 600;
      }

      .term-date-left {
        color: #000;
      }

      .term-date-right {
        color: #000;
      }

      /* School Motto Footer */
      .school-motto-footer {
        text-align: center;
        margin-top: 10px;
        padding-top: 8px;
        border-top: 1px solid #000;
      }

      .school-motto-footer p {
        font-size: 10px;
        font-style: italic;
        color: #000;
        margin: 0;
      }

      /* Document Footer */
      .document-footer {
        margin-top: 15px;
        text-align: right;
      }

      .date-printed {
        font-size: 10px;
        color: #000;
        font-weight: 600;
      }

      /* Print Styles */
      @media print {
        .report-card {
          margin: 0;
          padding: 5mm;
        }

        body {
          font-size: 11px;
        }

        .assessment-table {
          font-size: 9px;
        }

        .subject-row td {
          font-size: 8px;
        }
      }

      /* Page Break Styles for Bulk Reports */
      .page-break {
        page-break-before: always;
      }
    `;
  }


}

module.exports = PDFOLevelReportGenerator;
