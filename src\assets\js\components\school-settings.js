// SmartReport - School Settings Management Component
// Comprehensive school settings management system

// Uses global API services: window.SchoolSettingsAPI
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

const SchoolSettingsComponents = {
  // Component state
  state: {
    settings: {},
    loading: false,
    currentAdmin: null
  },

  // Initialize component
  async init() {
    await this.loadCurrentAdmin();
    await this.loadInitialData();
  },

  // Load current admin information
  async loadCurrentAdmin() {
    try {
      const currentAdminId = this.getCurrentAdminId();
      if (currentAdminId) {
        this.state.currentAdmin = {
          id: currentAdminId,
          name: this.getAdminName()
        };
      }
    } catch (error) {
      console.error('Failed to load current admin:', error);
      this.state.currentAdmin = null;
    }
  },



  // Get admin name for display
  getAdminName() {
    try {
      const adminData = localStorage.getItem('sr_admin_data');
      if (adminData) {
        const parsed = JSON.parse(adminData);
        return `${parsed.first_name || ''} ${parsed.last_name || ''}`.trim() || 'System Admin';
      }
      return 'System Admin';
    } catch (error) {
      return 'System Admin';
    }
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      console.log('🔄 Loading school settings data...');

      // Use the API service
      const result = await window.SchoolSettingsAPI.getAll();

      if (result.success && result.data) {
        // Convert key-value pairs to object for easier access
        this.state.settings = this.processSettingsData(result.data);
      } else {
        this.state.settings = {};
      }

      if (window.SRConfig && window.SRConfig.get('development.debugMode')) {
        console.log('✅ School settings data loaded:', this.state.settings);
      }

    } catch (error) {
      console.error('❌ Failed to load school settings:', error);
      if (window.SRConfig && window.SRConfig.get('development.debugMode')) {
        console.error('Debug: School settings loading error details:', error);
      }
      this.state.settings = {};
      // Show error notification if available
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load school settings', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  },

  // Convert settings array to object for easier access
  processSettingsData(settingsArray) {
    const settingsObj = {};
    if (Array.isArray(settingsArray)) {
      settingsArray.forEach(setting => {
        settingsObj[setting.setting_key] = {
          value: setting.setting_value,
          type: setting.setting_type,
          category: setting.category,
          description: setting.description,
          is_editable: setting.is_editable
        };
      });
    }
    return settingsObj;
  }
};

// School Settings Management Component
const SchoolSettingsManagementComponent = {
  // Render school settings interface
  render() {
    // Show loading state if data is still loading
    if (SchoolSettingsComponents.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading school settings data...');
      }
      return '';
    }

    return `
      <div class="space-y-6">
        ${this.renderPageHeader()}

        <!-- School Basic Information -->
        ${this.renderBasicInformation()}

        <!-- Contact Information -->
        ${this.renderContactInformation()}

        <!-- Actions -->
        ${this.renderActions()}
      </div>
    `;
  },

  // Render page header
  renderPageHeader() {
    
    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <div>
          <h1 class="${SRDesignSystem.responsive.text['2xl']} font-bold text-gray-900">School Settings</h1>
          <p class="text-gray-600 mt-1">Manage your school's basic information and configuration</p>
        </div>
      </div>
    `;
  },

  // Render basic information section
  renderBasicInformation() {
    const settings = SchoolSettingsComponents.state.settings;

    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Basic Information</h3>
        <form id="basic-info-form" class="space-y-6">

          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
            ${SRDesignSystem.forms.input('school_name', 'Name', settings.school_name?.value || '', {
              required: true,
              placeholder: 'Enter school name',
            })}
            ${SRDesignSystem.forms.input('school_motto', 'Motto', settings.school_motto?.value || '', {
              placeholder: 'Enter school motto',
            })}
          </div>

          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
            ${SRDesignSystem.forms.input('school_email', 'Email', settings.school_email?.value || '', {
              type: 'email',
              placeholder: '<EMAIL>',
            })}
            ${SRDesignSystem.forms.input('school_website', 'Website', settings.school_website?.value || '', {
              type: 'url',
              placeholder: 'www.example.com',
            })}
          </div>

          ${SRDesignSystem.forms.textarea('school_address', 'Address', settings.school_address?.value || '', {
            rows: 3,
            placeholder: 'Enter complete school address',
          })}

          <!-- School Logo Upload -->
          <div class="form-field">
            <label class="block ${SRDesignSystem.responsive.text.sm} font-medium text-gray-700 mb-2">
              School Logo
            </label>
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <img id="school-logo-preview"
                       class="h-20 w-20 rounded-lg object-cover border-2 border-gray-200"
                       src="${this.getSchoolLogoUrl(settings.school_logo?.value)}"
                       alt="School Logo"
                       onerror="this.src='${window.SR.serverUrl}/assets/images/default-school-logo.png'; this.onerror=null;">
                </div>
                <div class="flex-1">
                  <input type="file" id="school_logo" name="school_logo" accept="image/*"
                         onchange="SchoolSettingsManagementComponent.previewLogo(this)"
                         class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                  <p class="mt-1 text-xs text-gray-500">Upload a logo image (JPG, PNG - Max 2MB)</p>
                </div>
              </div>
            </div>
          </div>
          <input type="hidden" id="school_logo_path" name="school_logo_path" value="${settings.school_logo?.value || ''}">

        </form>
      </div>
    `;
  },

  // Initialize school settings component
  async init() {
    console.log('🔧 Initializing School Settings Component...');

    // Reset component state
    this.resetComponentState();

    // Load initial data first
    await SchoolSettingsComponents.loadInitialData();

    // Initialize directly - DOM should be ready due to lifecycle manager
    console.log('🔄 Populating School Settings UI...');
    this.initializeEventListeners();
    console.log('✅ School Settings Component initialized successfully');
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed
    console.log('🔄 School Settings Component state reset');
  },

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up School Settings Component...');
    this.resetComponentState();

    // Remove any event listeners or cleanup form state
    const form = document.getElementById('school-settings-form');
    if (form) {
      form.reset();
    }

    console.log('✅ School Settings Component cleanup completed');
  },

  // Render contact information section
  renderContactInformation() {
    const settings = SchoolSettingsComponents.state.settings;
    // Parse contact numbers from school_contacts setting (comma-separated string)
    const contactsString = settings.school_contacts?.value || '';
    const contactNumbers = contactsString.split(',').map(contact => contact.trim()).filter(contact => contact.length > 0);

    // Ensure at least one contact field is shown
    if (contactNumbers.length === 0) {
      contactNumbers.push('');
    }

    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Contact Information</h3>
        <div class="space-y-4">
          <div class="form-field">
            <label class="block ${SRDesignSystem.responsive.text.sm} font-medium text-gray-700 mb-2">
              Contact Numbers <span class="text-red-500">*</span>
            </label>
            <div id="contact-numbers-container" class="space-y-2">
              ${contactNumbers.map((number, index) => `
                <div class="flex items-center space-x-2">
                  <input type="tel"
                         id="contact_${index}"
                         name="contact_numbers[]"
                         value="${number}"
                         class="flex-1 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                         placeholder="e.g., +256 700 000 000">
                  ${index > 0 ? `
                    <button type="button" onclick="SchoolSettingsManagementComponent.removeContactNumber(${index})"
                            class="px-3 py-2 text-red-600 hover:text-red-800 transition-colors duration-200">
                      ${SRDesignSystem.components.icon('fas fa-trash', 'sm', 'current')}
                    </button>
                  ` : ''}
                </div>
              `).join('')}
            </div>
            <button type="button" onclick="SchoolSettingsManagementComponent.addContactNumber()"
                    class="mt-2 text-primary-600 hover:text-primary-800 ${SRDesignSystem.responsive.text.sm} transition-colors duration-200">
              ${SRDesignSystem.components.icon('fas fa-plus', 'sm', 'current')} <span class="ml-1">Add Contact Number</span>
            </button>
          </div>
        </div>
      </div>
    `;
  },



  // Render actions section
  renderActions() {
    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Save</h3>
            <p class="${SRDesignSystem.responsive.text.sm} text-gray-600">Update your school settings and configuration</p>
          </div>
          <div class="flex items-center space-x-4">
            ${SRDesignSystem.forms.button('save-settings', this.getSaveButtonText(), 'primary', {
              onclick: 'SchoolSettingsManagementComponent.saveSettings()',
              icon: this.getSaveButtonIcon()
            })}
          </div>
        </div>
      </div>
    `;
  },

  // Add contact number
  addContactNumber() {
    const container = document.getElementById('contact-numbers-container');
    if (!container) return;

    const currentInputs = container.querySelectorAll('input[name="contact_numbers[]"]');
    const newIndex = currentInputs.length;

    const newContactDiv = document.createElement('div');
    newContactDiv.className = 'flex items-center space-x-2';
    newContactDiv.innerHTML = `
      <input type="tel"
             id="contact_${newIndex}"
             name="contact_numbers[]"
             value=""
             class="flex-1 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
             placeholder="e.g., +256 700 000 000">
      <button type="button" onclick="SchoolSettingsManagementComponent.removeContactNumber(${newIndex})"
              class="px-3 py-2 text-red-600 hover:text-red-800 transition-colors duration-200">
        <i class="fas fa-trash"></i>
      </button>
    `;

    container.appendChild(newContactDiv);

    // Apply validation to the newly added contact input
    const newInput = newContactDiv.querySelector('input[name="contact_numbers[]"]');
    if (newInput) {
      this.applyPhoneValidation(newInput);
    }
  },

  // Remove contact number
  removeContactNumber(index) {
    const contactInput = document.getElementById(`contact_${index}`);
    if (contactInput && contactInput.parentElement) {
      contactInput.parentElement.remove();
    }
  },

  // Handle logo upload (fallback method when ImageUploadUtil is not available)
  async handleLogoUpload(input) {
    // Use ImageUploadUtil if available
    if (window.ImageUploadUtil) {
      try {
        await window.ImageUploadUtil.handleImageUpload(
          input,
          'school-logo',
          'logo-preview',
          'school_logo_path'
        );
      } catch (error) {
        console.error('Logo upload error:', error);
      }
      return;
    }

    // Fallback implementation
    if (!input.files || !input.files[0]) return;

    const file = input.files[0];

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file (PNG, JPG, GIF)');
      input.value = '';
      return;
    }

    // Validate file size (2MB max)
    if (file.size > 2 * 1024 * 1024) {
      alert('File size must be less than 2MB');
      input.value = '';
      return;
    }

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('logo', file);

      // Upload the file
      const response = await fetch(`${window.SRConfig.getApiUrl('/upload/school-logo')}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('smartreport_token')}`
        },
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        // Update the preview image
        const preview = document.getElementById('logo-preview');
        if (preview) {
          preview.src = result.filePath;
        }

        // Update the hidden field with the file path
        const pathField = document.getElementById('school_logo_path');
        if (pathField) {
          pathField.value = result.filePath;
        }

        // Show success notification
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Logo uploaded successfully!', 'success');
        }
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Logo upload error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to upload logo: ' + error.message, 'error');
      } else {
        alert('Failed to upload logo: ' + error.message);
      }
      input.value = '';
    }
  },

  // Get save button text based on whether settings exist
  getSaveButtonText() {
    const settings = SchoolSettingsComponents.state.settings;
    const hasExistingSettings = settings && Object.keys(settings).length > 0;
    return hasExistingSettings ? 'Update Settings' : 'Save Settings';
  },

  // Get save button icon based on whether settings exist
  getSaveButtonIcon() {
    const settings = SchoolSettingsComponents.state.settings;
    const hasExistingSettings = settings && Object.keys(settings).length > 0;
    return hasExistingSettings ? 'fas fa-edit' : 'fas fa-save';
  },

  // Get school logo URL for preview
  getSchoolLogoUrl(logoPath) {
    if (!logoPath) {
      return `${window.SR.serverUrl}/assets/images/default-school-logo.png`;
    }

    // If it's already a full URL, return as is
    if (logoPath.startsWith('http://') || logoPath.startsWith('https://')) {
      return logoPath;
    }

    // If it starts with a slash, it's relative to server root
    if (logoPath.startsWith('/')) {
      return `${window.SR.serverUrl}${logoPath}`;
    }

    // Otherwise, assume it's relative to the school uploads directory
    return `${window.SR.serverUrl}/assets/images/uploads/school/${logoPath}`;
  },

  // Preview logo in the interface and upload it
  async previewLogo(input) {
    if (!input.files || !input.files[0]) return;

    const file = input.files[0];

    // Use ImageUploadUtil for validation and upload
    if (window.ImageUploadUtil) {
      try {
        // First validate the file
        const validation = window.ImageUploadUtil.validateFile(file);
        if (!validation.valid) {
          if (window.SRDesignSystem?.notifications) {
            window.SRDesignSystem.notifications.show(validation.error, 'error');
          } else {
            alert(validation.error);
          }
          input.value = '';
          return;
        }

        // Show preview immediately for better UX
        const reader = new FileReader();
        reader.onload = function(e) {
          const preview = document.getElementById('school-logo-preview');
          if (preview) {
            preview.src = e.target.result;
          }
        };
        reader.readAsDataURL(file);

        // Show upload progress if available
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Uploading logo...', 'info');
        }

        // Upload the file using ImageUploadUtil
        const uploadResult = await window.ImageUploadUtil.uploadImage(file, 'school-logo');

        if (uploadResult.success) {
          // Update the hidden field with the uploaded file path
          const pathField = document.getElementById('school_logo_path');
          if (pathField) {
            pathField.value = uploadResult.filePath;
          }

          if (window.SRDesignSystem?.notifications) {
            window.SRDesignSystem.notifications.show('Logo uploaded successfully!', 'success');
          }
        } else {
          throw new Error(uploadResult.message || 'Upload failed');
        }

      } catch (error) {
        console.error('Logo upload error:', error);
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Failed to upload logo: ' + error.message, 'error');
        } else {
          alert('Failed to upload logo: ' + error.message);
        }

        // Reset the input and preview
        input.value = '';
        const preview = document.getElementById('school-logo-preview');
        if (preview) {
          preview.src = this.getSchoolLogoUrl(null); // Reset to default
        }
      }
    } else {
      // Fallback if ImageUploadUtil is not available
      console.warn('ImageUploadUtil not available, using basic preview only');

      // Basic validation - same as student registration pattern
      if (file.size > 2 * 1024 * 1024) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('File size must be less than 2MB', 'error');
        } else {
          alert('File size must be less than 2MB');
        }
        input.value = '';
        return;
      }

      // Validate file type (only JPG and PNG) - same as student registration pattern
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a JPG or PNG image file', 'error');
        } else {
          alert('Please select a JPG or PNG image file');
        }
        input.value = '';
        return;
      }

      // Show preview only
      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('school-logo-preview');
        if (preview) {
          preview.src = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    }
  },

  // Save settings
  async saveSettings() {
    try {
      // Collect form data (logo is already uploaded via previewLogo function)
      const formData = {
        school_name: document.getElementById('school_name').value,
        school_motto: document.getElementById('school_motto').value,
        school_email: document.getElementById('school_email').value,
        school_website: document.getElementById('school_website').value,
        school_address: document.getElementById('school_address').value,
        school_logo: document.getElementById('school_logo_path').value
      };

      // Collect contact numbers
      const contactInputs = document.querySelectorAll('input[name="contact_numbers[]"]');
      const contactNumbers = Array.from(contactInputs)
        .map(input => input.value.trim())
        .filter(value => value.length > 0)
        .join(', ');

      formData.school_contacts = contactNumbers;

      // Convert to API format (bulk update expects 'key' and 'value' properties)
      const settingsArray = Object.entries(formData).map(([key, value]) => ({
        key: key,
        value: value
      }));

      // Save via API using bulk update
      const result = await window.SchoolSettingsAPI.updateMultiple(settingsArray);

      if (result.success) {
        // Reload the settings data to reflect the saved changes
        await SchoolSettingsComponents.loadInitialData();

        // Update the button text to "Update Settings"
        this.updateSaveButton();

        // Show success notification if available
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('School settings updated successfully!', 'success');
        } else {
          alert('School settings updated successfully!');
        }
      } else {
        // Show error notification if available
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to update settings', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update settings'));
        }
      }
    } catch (error) {
      console.error('Save settings error:', error);
      if (window.SRConfig && window.SRConfig.get('development.debugMode')) {
        console.error('Debug: Save settings error details:', error);
      }
      // Show error notification if available
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to save settings', 'error');
      } else {
        alert('Error: Failed to save settings');
      }
    }
  },

  // Update save button text and icon after successful save
  updateSaveButton() {
    const saveButton = document.getElementById('save-settings');
    if (saveButton) {
      // Update button text
      const buttonText = saveButton.querySelector('.sr-button-text') || saveButton;
      if (buttonText.textContent) {
        buttonText.textContent = 'Update Settings';
      }

      // Update button icon
      const buttonIcon = saveButton.querySelector('i');
      if (buttonIcon) {
        buttonIcon.className = 'fas fa-edit';
      }
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Logo upload is handled via onchange attribute in the HTML
    // No additional event listener needed since previewLogo is called directly

    // Setup contact number validation
    this.setupContactNumberValidation();

    // Setup school information field validation
    this.setupSchoolInfoValidation();

    console.log('✅ Event listeners initialized for School Settings');
  },

  // Setup contact number validation (Ugandan phone number format)
  setupContactNumberValidation() {
    // Apply validation to existing contact number fields
    const existingContactInputs = document.querySelectorAll('input[name="contact_numbers[]"]');
    existingContactInputs.forEach(input => {
      this.applyPhoneValidation(input);
    });
  },

  // Setup school information field validation
  setupSchoolInfoValidation() {
    // School Name validation (UPPERCASE, single spaces only)
    this.setupSchoolNameValidation();

    // School Motto validation (Sentence case, single spaces allowed)
    this.setupSchoolMottoValidation();

    // School Email validation (standard email format)
    this.setupSchoolEmailValidation();

    // School Website validation (standard website format)
    this.setupSchoolWebsiteValidation();
  },

  // Setup school name validation (UPPERCASE, single spaces only)
  setupSchoolNameValidation() {
    const schoolNameField = document.getElementById('school_name');
    if (!schoolNameField) return;

    schoolNameField.addEventListener('keydown', (e) => {
      // Allow navigation and control keys
      const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

      // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
      if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
          allowedKeys.includes(e.key)) {
        return;
      }

      // Allow letters and single spaces
      if (!/^[a-zA-Z\s]$/.test(e.key)) {
        e.preventDefault();
      }
    });

    schoolNameField.addEventListener('input', (e) => {
      let value = e.target.value;

      // Convert to uppercase and remove non-letter/space characters
      value = value.toUpperCase().replace(/[^A-Z\s]/g, '');

      // Replace multiple spaces with single spaces
      value = value.replace(/\s+/g, ' ');

      // Remove leading/trailing spaces during typing (but allow them temporarily)
      if (value !== value.trim() && value.trim().length > 0) {
        // Only trim if there's actual content
        value = value.trim();
      }

      e.target.value = value;

      // Validate required field
      if (!value.trim()) {
        e.target.setCustomValidity('School name is required');
      } else {
        e.target.setCustomValidity('');
      }
    });

    schoolNameField.addEventListener('paste', (e) => {
      e.preventDefault();
      let paste = '';
      if (e.clipboardData) {
        paste = e.clipboardData.getData('text');
      }
      // Convert to uppercase, remove non-letter/space chars, normalize spaces
      paste = paste.toUpperCase().replace(/[^A-Z\s]/g, '').replace(/\s+/g, ' ').trim();
      e.target.value = paste;
      e.target.dispatchEvent(new Event('input'));
    });
  },

  // Setup school motto validation (Sentence case, single spaces allowed)
  setupSchoolMottoValidation() {
    const schoolMottoField = document.getElementById('school_motto');
    if (!schoolMottoField) return;

    schoolMottoField.addEventListener('input', (e) => {
      let value = e.target.value;

      // Replace multiple spaces with single spaces
      value = value.replace(/\s+/g, ' ');

      // Convert to sentence case (first letter uppercase, rest lowercase)
      if (value.length > 0) {
        value = value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
      }

      e.target.value = value;
    });

    schoolMottoField.addEventListener('paste', (e) => {
      setTimeout(() => {
        let value = e.target.value;
        // Normalize spaces and apply sentence case
        value = value.replace(/\s+/g, ' ').trim();
        if (value.length > 0) {
          value = value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
        }
        e.target.value = value;
      }, 0);
    });
  },

  // Setup school email validation (standard email format)
  setupSchoolEmailValidation() {
    const schoolEmailField = document.getElementById('school_email');
    if (!schoolEmailField) return;

    schoolEmailField.addEventListener('input', (e) => {
      const value = e.target.value.trim();

      if (value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          e.target.setCustomValidity('Please enter a valid email address (e.g., <EMAIL>)');
        } else {
          e.target.setCustomValidity('');
        }
      } else {
        e.target.setCustomValidity('');
      }
    });

    schoolEmailField.addEventListener('blur', (e) => {
      const value = e.target.value.trim();
      e.target.value = value; // Remove any trailing spaces

      if (value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          e.target.setCustomValidity('Please enter a valid email address (e.g., <EMAIL>)');
        } else {
          e.target.setCustomValidity('');
        }
      }
    });
  },

  // Setup school website validation (standard website format)
  setupSchoolWebsiteValidation() {
    const schoolWebsiteField = document.getElementById('school_website');
    if (!schoolWebsiteField) return;

    schoolWebsiteField.addEventListener('input', (e) => {
      let value = e.target.value.trim().toLowerCase();

      if (value) {
        // Add https:// if no protocol is specified
        if (!value.startsWith('http://') && !value.startsWith('https://')) {
          value = 'https://' + value;
          e.target.value = value;
        }

        // Validate URL format
        try {
          const url = new URL(value);
          if (!url.hostname.includes('.')) {
            e.target.setCustomValidity('Please enter a valid website URL (e.g., https://www.school.edu.ug)');
          } else {
            e.target.setCustomValidity('');
          }
        } catch {
          e.target.setCustomValidity('Please enter a valid website URL (e.g., https://www.school.edu.ug)');
        }
      } else {
        e.target.setCustomValidity('');
      }
    });

    schoolWebsiteField.addEventListener('blur', (e) => {
      let value = e.target.value.trim().toLowerCase();
      e.target.value = value; // Remove any trailing spaces and ensure lowercase

      if (value) {
        // Ensure protocol is present
        if (!value.startsWith('http://') && !value.startsWith('https://')) {
          value = 'https://' + value;
          e.target.value = value;
        }

        // Final validation
        try {
          const url = new URL(value);
          if (!url.hostname.includes('.')) {
            e.target.setCustomValidity('Please enter a valid website URL (e.g., https://www.school.edu.ug)');
          } else {
            e.target.setCustomValidity('');
          }
        } catch {
          e.target.setCustomValidity('Please enter a valid website URL (e.g., https://www.school.edu.ug)');
        }
      }
    });
  },

  // Apply phone validation to a contact input field
  applyPhoneValidation(phoneField) {
    if (!phoneField) return;

    // Allow only digits, +, and spaces during input
    phoneField.addEventListener('input', (e) => {
      let value = e.target.value;
      // Remove any characters that aren't digits, +, or spaces
      value = value.replace(/[^\d\+\s]/g, '');
      e.target.value = value;

      // Validate against the Ugandan phone number pattern
      if (value) {
        const pattern = /^(\+256|0)(7[0-9]{8}|3[0-9]{8}|4[0-9]{8})$/;
        if (!pattern.test(value.replace(/\s/g, ''))) {
          e.target.setCustomValidity('Enter a valid Ugandan phone number (e.g., +256 700 000 000 or 0700 000 000)');
        } else {
          e.target.setCustomValidity('');
        }
      } else {
        e.target.setCustomValidity('');
      }
    });

    // Format on blur (remove spaces for validation, but keep for display)
    phoneField.addEventListener('blur', (e) => {
      let value = e.target.value.replace(/\s/g, ''); // Remove spaces for validation
      if (value) {
        const pattern = /^(\+256|0)(7[0-9]{8}|3[0-9]{8}|4[0-9]{8})$/;
        if (!pattern.test(value)) {
          e.target.setCustomValidity('Enter a valid Ugandan phone number (e.g., +256 700 000 000 or 0700 000 000)');
        } else {
          e.target.setCustomValidity('');
        }
      }
    });
  }
};

// Export components to global scope
window.SchoolSettingsComponents = SchoolSettingsComponents;
window.SchoolSettingsManagementComponent = SchoolSettingsManagementComponent;
